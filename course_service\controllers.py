"""
Controllers for the Course Service.

This module contains the business logic for course management.

English: This file contains the logic for course management
Tanglish: Indha file-la course management-kku logic irukku
"""

from flask import jsonify, request
from course_service.common.utils import handle_error, validate_request_data, call_service
from course_service.models import Course, StudentCourse
from course_service.common.db_config import db

def create_course():
    """
    Create a new course.

    Returns:
        JSON response with the created course

    English: This function creates a new course
    Tanglish: Indha function puthusa oru course-a create pannum
    """
    # Get request data
    data = request.get_json()
    if not data:
        return handle_error("No data provided", 400)

    # Check if required fields are present
    required_fields = ['name']
    missing_fields = [field for field in required_fields if field not in data]
    if missing_fields:
        return handle_error(f"Missing required fields: {', '.join(missing_fields)}", 400)

    # Get user role from request environment
    user_role = request.environ.get('user_role')
    print(f"User role from environment: '{user_role}'")

    # Check if the user has permission to create a course
    if user_role not in ['Super Admin', 'Admin']:
        print(f"Permission denied. User role '{user_role}' cannot create a course.")
        return handle_error("You don't have permission to create a course", 403)

    print(f"Permission granted. User role '{user_role}' can create a course.")

    # Check if course name already exists
    if Course.query.filter_by(name=data['name']).first():
        return handle_error("Course name already exists", 400)

    # Create new course
    course = Course(
        name=data['name'],
        description=data.get('description')
    )

    # Save course to database
    db.session.add(course)
    db.session.commit()

    # Return course information
    return jsonify({
        "message": "Course created successfully",
        "course": course.to_dict()
    }), 201

def get_courses():
    """
    Get all courses.

    Returns:
        JSON response with all courses

    English: This function gets all courses
    Tanglish: Indha function ella courses-um get pannum
    """
    # Get all courses
    courses = Course.query.all()

    # Return courses
    return jsonify({
        "courses": [course.to_dict() for course in courses]
    })

def get_course(course_id):
    """
    Get a specific course.

    Args:
        course_id: ID of the course to get

    Returns:
        JSON response with the course

    English: This function gets a specific course
    Tanglish: Indha function specific course-a get pannum
    """
    # Get the course
    course = Course.query.get(course_id)
    if not course:
        return handle_error("Course not found", 404)

    # Return course
    return jsonify({
        "course": course.to_dict()
    })

def map_student_to_course():
    """
    Map a student to a course using the new one-to-one relationship in student table.

    Returns:
        JSON response with the mapping

    English: This function maps a student to a course (one-to-one) by calling student service
    Tanglish: Indha function oru student-a oru course-oda map pannum (one-to-one) student service-a call panni
    """
    # Get request data
    data = request.get_json()
    if not data:
        return handle_error("No data provided", 400)

    # Check if required fields are present
    required_fields = ['student_user_id', 'course_id']
    missing_fields = [field for field in required_fields if field not in data]
    if missing_fields:
        return handle_error(f"Missing required fields: {', '.join(missing_fields)}", 400)

    # Get user role from request environment
    user_role = request.environ.get('user_role')

    # Check if the user has permission to map a student to a course
    if user_role not in ['Super Admin', 'Admin', 'Teacher']:
        return handle_error("You don't have permission to map a student to a course", 403)

    # Check if the course exists
    course = Course.query.get(data['course_id'])
    if not course:
        return handle_error("Course not found", 404)

    # Call the Student Service to map the course to the student
    student_service_url = request.environ.get('STUDENT_SERVICE_URL', 'http://localhost:5002')
    map_course_url = f"{student_service_url}/api/students/map-course"

    # Get the Authorization header from the request
    auth_header = request.headers.get('Authorization')
    headers = {'Authorization': auth_header, 'Content-Type': 'application/json'} if auth_header else {'Content-Type': 'application/json'}

    # Prepare data for student service
    student_service_data = {
        'student_user_id': data['student_user_id'],
        'course_id': data['course_id'],
        'course_name': course.name
    }

    # Call the Student Service
    response = call_service(map_course_url, headers=headers, method='POST', data=student_service_data)
    if not response or response.status_code not in [200, 201]:
        error_message = "Failed to map course to student"
        if response:
            try:
                error_data = response.json()
                error_message = error_data.get('error', error_message)
            except:
                pass
        return handle_error(error_message, response.status_code if response else 500)

    # Return the response from student service
    return response.json(), response.status_code

def get_student_courses(student_user_id):
    """
    Get course for a specific student using user_id (one-to-one relationship).

    Args:
        student_user_id: User ID of the student

    Returns:
        JSON response with the student's course

    English: This function gets the course for a specific student using user_id
    Tanglish: Indha function specific student-kku user_id use panni course-a get pannum
    """
    # Call the Student Service to get the student with course information
    student_service_url = request.environ.get('STUDENT_SERVICE_URL', 'http://localhost:5002')

    # Get the Authorization header from the request
    auth_header = request.headers.get('Authorization')
    headers = {'Authorization': auth_header} if auth_header else None

    # First, get the student by user_id to find the student ID
    students_url = f"{student_service_url}/api/students/students"
    response = call_service(students_url, headers=headers)

    if not response or response.status_code != 200:
        return handle_error("Failed to get student information", 500)

    try:
        students_data = response.json()
        students = students_data.get('students', [])

        # Find the student with matching user_id
        student = None
        for s in students:
            if s.get('user_id') == student_user_id:
                student = s
                break

        if not student:
            return handle_error("Student not found", 404)

        # Return course information from student data
        course_info = {
            'course_id': student.get('course_id'),
            'course_name': student.get('course_name')
        }

        return jsonify({
            "course": course_info if course_info['course_id'] else None
        })

    except Exception as e:
        return handle_error(f"Error processing student data: {str(e)}", 500)
