"""
Models for the Student Service.

This module defines the database models for the Student Service.

English: This file defines the database tables for the Student Service
Tanglish: Indha file Student Service-kku database tables-a define pannum
"""

from datetime import datetime
from student_service.common.db_config import db

class Student(db.Model):
    """
    Student model.

    English: This model stores student information including course mapping
    Tanglish: Indha model student information-a course mapping-oda store pannum
    """
    __tablename__ = 'students'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, nullable=False, unique=True)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    date_of_birth = db.Column(db.Date, nullable=True)
    address = db.Column(db.Text, nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    # Course mapping columns - one student maps to one course only
    course_id = db.Column(db.Integer, nullable=True)  # Foreign key to courses table
    course_name = db.Column(db.String(100), nullable=True)  # Course name for easy access
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, user_id, first_name, last_name, date_of_birth=None, address=None, phone=None, course_id=None, course_name=None):
        """
        Initialize a new Student.

        Args:
            user_id: ID of the user associated with this student
            first_name: Student's first name
            last_name: Student's last name
            date_of_birth: Student's date of birth (optional)
            address: Student's address (optional)
            phone: Student's phone number (optional)
            course_id: ID of the course the student is enrolled in (optional)
            course_name: Name of the course the student is enrolled in (optional)

        English: This function creates a new student with the given information including course mapping
        Tanglish: Indha function kudukkapatta information-oda course mapping-oda puthusa oru student-a create pannum
        """
        self.user_id = user_id
        self.first_name = first_name
        self.last_name = last_name
        self.date_of_birth = date_of_birth
        self.address = address
        self.phone = phone
        self.course_id = course_id
        self.course_name = course_name
        
    def to_dict(self):
        """
        Convert the student to a dictionary.

        Returns:
            Dictionary representation of the student including course information

        English: This function converts the student to a dictionary including course information
        Tanglish: Indha function student-a course information-oda dictionary-a convert pannum
        """
        return {
            'id': self.id,
            'user_id': self.user_id,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'date_of_birth': self.date_of_birth.isoformat() if self.date_of_birth else None,
            'address': self.address,
            'phone': self.phone,
            'course_id': self.course_id,
            'course_name': self.course_name,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class ParentStudent(db.Model):
    """
    ParentStudent model for one-to-one relationship between parents and students using user_id.

    English: This model links parents to students (one-to-one) using user_id for consistency
    Tanglish: Indha model parents-a students-oda user_id use panni link pannum (one-to-one)
    """
    __tablename__ = 'parent_students'

    id = db.Column(db.Integer, primary_key=True)
    parent_id = db.Column(db.Integer, nullable=False)  # Parent's ID (keeping original name for now)
    user_id = db.Column(db.Integer, nullable=False)  # Student's user_id (changed from student_id)
    relationship = db.Column(db.String(20), nullable=True)  # e.g., Father, Mother, Guardian
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __init__(self, parent_id, user_id, relationship=None):
        """
        Initialize a new ParentStudent.

        Args:
            parent_id: ID of the parent
            user_id: User ID of the student (matches database column name)
            relationship: Relationship between parent and student (optional)

        English: This function links a parent to a student using user_id for student
        Tanglish: Indha function oru parent-a oru student-oda user_id use panni link pannum
        """
        self.parent_id = parent_id
        self.user_id = user_id  # This matches the database column name
        self.relationship = relationship
        
    def to_dict(self):
        """
        Convert the parent student to a dictionary.

        Returns:
            Dictionary representation of the parent student using database column names

        English: This function converts the parent student to a dictionary using database column names
        Tanglish: Indha function parent student-a database column names use panni dictionary-a convert pannum
        """
        return {
            'id': self.id,
            'parent_id': self.parent_id,
            'user_id': self.user_id,  # Student's user_id (matches database column)
            'relationship': self.relationship,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
