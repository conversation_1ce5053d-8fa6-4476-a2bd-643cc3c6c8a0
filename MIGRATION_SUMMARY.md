# Database Migration Summary: Student-Course Mapping Changes

## Overview
This migration implements the requested changes to modify student-to-course mapping from many-to-many to one-to-one relationship and ensures consistent use of `user_id` across all tables.

## Database Changes

### 1. Column Renames
- **`public.student_courses.student_id`** → **`public.student_courses.user_id`**
- **`public.parent_students.student_id`** → **`public.parent_students.user_id`**

### 2. Student Table Enhancement
- Added `course_id` column (INTEGER, nullable)
- Added `course_name` column (VARCHAR(100), nullable)

## Code Changes

### Backend Changes

#### 1. Student Service (`student_service/`)
- **models.py**: 
  - Added course columns to Student model
  - Updated ParentStudent model to use `user_id` instead of `student_id`
  - Updated `__init__` and `to_dict` methods

- **controllers.py**:
  - Added `map_course_to_student()` function for one-to-one course mapping
  - Updated `register_student()` to accept course information
  - Updated `map_parent_to_student()` to use correct column names
  - Updated `get_parent_students()` to use `parent_id`

- **views.py**:
  - Added `/map-course` endpoint
  - Updated parent-students endpoint parameter names

#### 2. Course Service (`course_service/`)
- **models.py**:
  - Updated StudentCourse model to use `user_id` instead of `student_id`
  - Updated `__init__` and `to_dict` methods

- **controllers.py**:
  - Modified `map_student_to_course()` to call student service
  - Updated `get_student_courses()` to work with new structure
  - Changed to use `student_user_id` parameter

- **views.py**:
  - Updated endpoints to use `student_user_id` instead of `student_id`

### Frontend Changes

#### 1. Course Service (`frontend/src/services/courseService/`)
- Updated API calls to use `student_user_id` instead of `student_id`
- Updated `mapStudentToCourse()` and `getStudentCourses()` functions

#### 2. Student Service (`frontend/src/services/studentService/`)
- Added `studentMapCourseService()` for course mapping
- Updated parent-students service parameter names

## Migration Files

### 1. SQL Migration Scripts
- **`database_migrations/001_rename_student_id_to_user_id.sql`**: Forward migration
- **`database_migrations/001_rollback_rename_student_id_to_user_id.sql`**: Rollback script

### 2. Python Migration Script
- **`run_database_migration.py`**: Automated migration script with verification

## API Changes

### New Endpoints
- `POST /api/students/map-course` - Map course to student (one-to-one)

### Updated Endpoints
- `POST /api/courses/map-student` - Now expects `student_user_id` instead of `student_id`
- `GET /api/courses/student-courses/<student_user_id>` - Uses user_id parameter
- `POST /api/students/map-parent` - Now expects `parent_id` and `student_user_id`

### Request/Response Format Changes

#### Course Mapping (NEW)
```json
POST /api/students/map-course
{
  "student_user_id": 123,
  "course_id": 456,
  "course_name": "NEET Preparation"
}
```

#### Student-Course Mapping (UPDATED)
```json
POST /api/courses/map-student
{
  "student_user_id": 123,  // Changed from student_id
  "course_id": 456
}
```

#### Parent-Student Mapping (UPDATED)
```json
POST /api/students/map-parent
{
  "parent_id": 789,        // Changed from parent_user_id
  "student_user_id": 123   // Changed from student_user_id
}
```

## Key Features Implemented

### 1. One-to-One Course Mapping
- Each student can have only one course
- Course information stored directly in student table
- Validation prevents multiple course assignments

### 2. Consistent User ID Usage
- All student relationships now use `user_id`
- Database column names match code expectations
- Improved data consistency across services

### 3. Backward Compatibility
- Existing APIs updated with proper parameter names
- Migration scripts handle existing data
- Rollback capability provided

## Running the Migration

### Option 1: Automated Python Script
```bash
# Set environment variables
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=pandor2_fl
export DB_USER=postgres
export DB_PASSWORD=your_password

# Install dependencies
pip install psycopg2-binary

# Run migration
python run_database_migration.py
```

### Option 2: Manual SQL Execution
```bash
# Connect to PostgreSQL
psql -h localhost -U postgres -d pandor2_fl

# Run migration script
\i database_migrations/001_rename_student_id_to_user_id.sql
```

## Verification Steps

1. **Database Structure**:
   ```sql
   \d public.student_courses
   \d public.parent_students
   \d public.students
   ```

2. **Test API Endpoints**:
   - Test course mapping with new endpoint
   - Verify parent-student mapping works
   - Check student registration with course info

3. **Frontend Integration**:
   - Test course assignment functionality
   - Verify parent-student relationships display correctly

## Rollback Procedure

If rollback is needed:
```bash
psql -h localhost -U postgres -d pandor2_fl -f database_migrations/001_rollback_rename_student_id_to_user_id.sql
```

## Notes

- All datetime fields use deprecated `datetime.utcnow()` - consider updating to `datetime.now(timezone.utc)` in future
- StudentCourse model kept for backward compatibility but marked as deprecated
- Consider adding database indexes on new `user_id` columns for performance
- Test thoroughly in development environment before production deployment
