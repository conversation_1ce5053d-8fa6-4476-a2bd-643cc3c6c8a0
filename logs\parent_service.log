2025-05-13 15:15:52,809 - parent_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/parents/parents
2025-05-13 15:48:31,362 - parent_service.common.utils - ERROR - Error: Parent with this user_id already exists, Status: 400
2025-05-13 15:51:59,384 - parent_service.common.utils - ERROR - Error: Parent with this user_id already exists, Status: 400
2025-05-13 16:00:05,691 - parent_service.common.utils - ERROR - Error: Parent with this user_id already exists, Status: 400
2025-05-28 13:05:03,668 - parent_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/parents/parents
2025-05-28 13:05:03,676 - parent_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/parents/parents
2025-05-28 13:05:14,655 - parent_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/parents/parents
2025-05-28 13:05:14,978 - parent_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/parents/parents
2025-05-28 13:05:45,662 - parent_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/parents/parents
2025-05-28 13:05:45,673 - parent_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/parents/parents
2025-05-28 13:05:52,655 - parent_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/parents/parents
2025-05-28 13:05:52,662 - parent_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/parents/parents
2025-05-29 11:20:57,005 - parent_service.common.utils - ERROR - Error: Missing required fields: user_id, first_name, last_name, Status: 400
2025-05-29 11:29:59,838 - parent_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/parents/parents
