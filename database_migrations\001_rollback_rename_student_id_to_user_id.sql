-- Rollback Script: Revert user_id columns back to student_id
-- Date: $(date)
-- Description: Rollback changes - rename user_id columns back to student_id in student_courses and parent_students tables

-- Start transaction
BEGIN;

-- 1. Rename user_id back to student_id in public.student_courses table
ALTER TABLE public.student_courses 
RENAME COLUMN user_id TO student_id;

-- 2. Rename user_id back to student_id in public.parent_students table  
ALTER TABLE public.parent_students 
RENAME COLUMN user_id TO student_id;

-- Add comments to document the rollback
COMMENT ON COLUMN public.student_courses.student_id IS 'Student ID (reverted from user_id)';
COMMENT ON COLUMN public.parent_students.student_id IS 'Student ID (reverted from user_id)';

-- Commit the transaction
COMMIT;

-- Verify the rollback
SELECT 
    table_name, 
    column_name, 
    data_type 
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name IN ('student_courses', 'parent_students')
    AND column_name = 'student_id'
ORDER BY table_name, column_name;
