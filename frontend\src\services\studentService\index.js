import axios from "axios";
import config from "../../config";
import { authService } from "../authService";

export const studentService = {
  // API Service Methods
  studentRegisterService: (data) => {
    return axios.post(`${config.API.STUDENTS}/students`, data);
  },
  studentGetAllService: () => {
    return axios.get(`${config.API.STUDENTS}/students`);
  },
  studentGetByIdService: (studentId) => {
    return axios.get(`${config.API.STUDENTS}/students/${studentId}`);
  },
  studentMapCourseService: (data) => {
    return axios.post(`${config.API.STUDENTS}/map-course`, data);
  },
  studentMapParentService: (data) => {
    return axios.post(`${config.API.STUDENTS}/map-parent`, data);
  },
  studentGetParentStudentsService: (parentUserId) => {
    return axios.get(`${config.API.STUDENTS}/parent-students/${parentUserId}`);
  },
  studentGetProfileService: () => {
    return axios.get(`${config.API.STUDENTS}/student-profile`);
  },


  // Utility Methods (for backward compatibility)
  registerStudent: async (studentData) => {
    try {
      const response = await axios.post(`${config.API.STUDENTS}/students`, studentData, {
        headers: authService.getAuthHeader()
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  getStudents: async () => {
    try {
      const response = await axios.get(`${config.API.STUDENTS}/students`, {
        headers: authService.getAuthHeader()
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  getStudent: async (studentId) => {
    try {
      const response = await axios.get(`${config.API.STUDENTS}/students/${studentId}`, {
        headers: authService.getAuthHeader()
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  mapParentToStudent: async (parentId, studentId, relationship = null) => {
    try {
      const response = await axios.post(`${config.API.STUDENTS}/map-parent`, {
        parent_id: parentId,
        student_id: studentId,
        relationship: relationship
      }, {
        headers: authService.getAuthHeader()
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  getParentStudents: async (parentId) => {
    try {
      const response = await axios.get(`${config.API.STUDENTS}/parent-students/${parentId}`, {
        headers: authService.getAuthHeader()
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  getStudentProfile: async () => {
    try {
      const response = await axios.get(`${config.API.STUDENTS}/student-profile`, {
        headers: authService.getAuthHeader(),
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      try {
        const currentUser = authService.getCurrentUser();
        if (!currentUser) {
          throw { error: 'User not authenticated' };
        }

        const data = await studentService.getStudents();
        const students = data.students || [];
        const currentStudent = students.find(s => s.user_id === parseInt(currentUser.id));

        if (!currentStudent) {
          throw { error: 'Student not found' };
        }

        return { student: currentStudent };
      } catch (fallbackError) {
        throw fallbackError.error ? fallbackError : { error: 'Failed to get student profile' };
      }
    }
  },

  getStudentParents: async (studentId) => {
    try {
      const response = await axios.get(`${config.API.STUDENTS}/student-parents/${studentId}`, {
        headers: authService.getAuthHeader()
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },
};
