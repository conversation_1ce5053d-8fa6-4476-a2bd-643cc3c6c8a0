#!/usr/bin/env python3
"""
Database Migration Script for Student-Course Mapping Changes

This script runs the database migration to rename student_id columns to user_id
in the student_courses and parent_students tables.

Usage:
    python run_database_migration.py

Requirements:
    - psycopg2 (pip install psycopg2-binary)
    - PostgreSQL database connection details
"""

import os
import sys
import psycopg2
from psycopg2 import sql
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Database connection parameters
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': os.getenv('DB_PORT', '5432'),
    'database': os.getenv('DB_NAME', 'pandor2_fl'),
    'user': os.getenv('DB_USER', 'postgres'),
    'password': os.getenv('DB_PASSWORD', 'password')
}

def connect_to_database():
    """Connect to PostgreSQL database."""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        conn.autocommit = False  # We'll handle transactions manually
        logger.info("Successfully connected to database")
        return conn
    except psycopg2.Error as e:
        logger.error(f"Error connecting to database: {e}")
        return None

def run_migration(conn):
    """Run the database migration."""
    try:
        cursor = conn.cursor()
        
        logger.info("Starting database migration...")
        
        # Start transaction
        cursor.execute("BEGIN;")
        
        # Check if columns exist before renaming
        logger.info("Checking current table structure...")
        
        # Check student_courses table
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
                AND table_name = 'student_courses' 
                AND column_name IN ('student_id', 'user_id')
        """)
        student_courses_columns = [row[0] for row in cursor.fetchall()]
        
        # Check parent_students table
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
                AND table_name = 'parent_students' 
                AND column_name IN ('student_id', 'user_id')
        """)
        parent_students_columns = [row[0] for row in cursor.fetchall()]
        
        logger.info(f"student_courses columns found: {student_courses_columns}")
        logger.info(f"parent_students columns found: {parent_students_columns}")
        
        # Rename student_id to user_id in student_courses table if needed
        if 'student_id' in student_courses_columns and 'user_id' not in student_courses_columns:
            logger.info("Renaming student_id to user_id in student_courses table...")
            cursor.execute("""
                ALTER TABLE public.student_courses 
                RENAME COLUMN student_id TO user_id;
            """)
            cursor.execute("""
                COMMENT ON COLUMN public.student_courses.user_id 
                IS 'User ID of the student (renamed from student_id for consistency)';
            """)
            logger.info("✓ student_courses.student_id renamed to user_id")
        elif 'user_id' in student_courses_columns:
            logger.info("✓ student_courses.user_id already exists, skipping rename")
        else:
            logger.warning("⚠ student_courses table structure unexpected")
        
        # Rename student_id to user_id in parent_students table if needed
        if 'student_id' in parent_students_columns and 'user_id' not in parent_students_columns:
            logger.info("Renaming student_id to user_id in parent_students table...")
            cursor.execute("""
                ALTER TABLE public.parent_students 
                RENAME COLUMN student_id TO user_id;
            """)
            cursor.execute("""
                COMMENT ON COLUMN public.parent_students.user_id 
                IS 'User ID of the student (renamed from student_id for consistency)';
            """)
            logger.info("✓ parent_students.student_id renamed to user_id")
        elif 'user_id' in parent_students_columns:
            logger.info("✓ parent_students.user_id already exists, skipping rename")
        else:
            logger.warning("⚠ parent_students table structure unexpected")
        
        # Commit the transaction
        cursor.execute("COMMIT;")
        
        # Verify the changes
        logger.info("Verifying migration results...")
        cursor.execute("""
            SELECT 
                table_name, 
                column_name, 
                data_type 
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
                AND table_name IN ('student_courses', 'parent_students')
                AND column_name = 'user_id'
            ORDER BY table_name, column_name;
        """)
        
        results = cursor.fetchall()
        logger.info("Migration verification:")
        for table_name, column_name, data_type in results:
            logger.info(f"  ✓ {table_name}.{column_name} ({data_type})")
        
        cursor.close()
        logger.info("Database migration completed successfully!")
        return True
        
    except psycopg2.Error as e:
        logger.error(f"Error during migration: {e}")
        try:
            cursor.execute("ROLLBACK;")
            logger.info("Transaction rolled back")
        except:
            pass
        return False

def main():
    """Main function to run the migration."""
    logger.info("Starting database migration script...")
    
    # Connect to database
    conn = connect_to_database()
    if not conn:
        logger.error("Failed to connect to database. Exiting.")
        sys.exit(1)
    
    try:
        # Run migration
        success = run_migration(conn)
        
        if success:
            logger.info("Migration completed successfully!")
            sys.exit(0)
        else:
            logger.error("Migration failed!")
            sys.exit(1)
            
    finally:
        conn.close()
        logger.info("Database connection closed")

if __name__ == "__main__":
    main()
