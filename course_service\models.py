"""
Models for the Course Service.

This module defines the database models for the Course Service.

English: This file defines the database tables for the Course Service
Tanglish: Indha file Course Service-kku database tables-a define pannum
"""

from datetime import datetime
from course_service.common.db_config import db

class Course(db.Model):
    """
    Course model.
    
    English: This model stores course information
    Tanglish: Indha model course information-a store pannum
    """
    __tablename__ = 'courses'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, name, description=None):
        """
        Initialize a new Course.
        
        Args:
            name: Course name
            description: Course description (optional)
            
        English: This function creates a new course with the given information
        Tanglish: Indha function kudukkapatta information-oda puthusa oru course-a create pannum
        """
        self.name = name
        self.description = description
        
    def to_dict(self):
        """
        Convert the course to a dictionary.
        
        Returns:
            Dictionary representation of the course
            
        English: This function converts the course to a dictionary
        Tanglish: Indha function course-a dictionary-a convert pannum
        """
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class StudentCourse(db.Model):
    """
    StudentCourse model for many-to-many relationship between students and courses.

    English: This model links students to courses using user_id
    Tanglish: Indha model students-a courses-oda user_id use panni link pannum
    """
    __tablename__ = 'student_courses'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, nullable=False)  # Changed from student_id to user_id
    course_id = db.Column(db.Integer, db.ForeignKey('courses.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Define relationship to Course
    course = db.relationship('Course', backref=db.backref('student_courses', lazy=True))
    
    def __init__(self, user_id, course_id):
        """
        Initialize a new StudentCourse.

        Args:
            user_id: User ID of the student
            course_id: ID of the course

        English: This function links a student to a course using user_id
        Tanglish: Indha function oru student-a oru course-oda user_id use panni link pannum
        """
        self.user_id = user_id
        self.course_id = course_id
        
    def to_dict(self):
        """
        Convert the student course to a dictionary.

        Returns:
            Dictionary representation of the student course using user_id

        English: This function converts the student course to a dictionary using user_id
        Tanglish: Indha function student course-a user_id use panni dictionary-a convert pannum
        """
        return {
            'id': self.id,
            'user_id': self.user_id,  # Changed from student_id to user_id
            'course_id': self.course_id,
            'course': self.course.to_dict() if self.course else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
