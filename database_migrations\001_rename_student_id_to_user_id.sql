-- Migration Script: Rename student_id columns to user_id
-- Date: $(date)
-- Description: Change student_id column names to user_id in student_courses and parent_students tables

-- Start transaction
BEGIN;

-- 1. Rename student_id to user_id in public.student_courses table
ALTER TABLE public.student_courses 
RENAME COLUMN student_id TO user_id;

-- 2. Rename student_id to user_id in public.parent_students table  
ALTER TABLE public.parent_students 
RENAME COLUMN student_id TO user_id;

-- Add comments to document the changes
COMMENT ON COLUMN public.student_courses.user_id IS 'User ID of the student (renamed from student_id for consistency)';
COMMENT ON COLUMN public.parent_students.user_id IS 'User ID of the student (renamed from student_id for consistency)';

-- Commit the transaction
COMMIT;

-- Verify the changes
SELECT 
    table_name, 
    column_name, 
    data_type 
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name IN ('student_courses', 'parent_students')
    AND column_name = 'user_id'
ORDER BY table_name, column_name;
