2025-05-08 13:46:54,247 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-05-08 13:46:54,248 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 13:46:54,251 - werkzeug - INFO -  * Restarting with stat
2025-05-08 13:46:55,550 - werkzeug - WARNING -  * Debugger is active!
2025-05-08 13:46:55,553 - werkzeug - INFO -  * Debugger PIN: 672-************-05-08 13:47:38,671 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://***********:5001
2025-05-08 13:47:38,671 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 13:47:38,673 - werkzeug - INFO -  * Restarting with stat
2025-05-08 13:47:39,880 - werkzeug - WARNING -  * Debugger is active!
2025-05-08 13:47:39,883 - werkzeug - INFO -  * Debugger PIN: 672-************-05-08 13:49:28,354 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-05-08 13:49:28,355 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 13:49:28,356 - werkzeug - INFO -  * Restarting with stat
2025-05-08 13:49:29,518 - werkzeug - WARNING -  * Debugger is active!
2025-05-08 13:49:29,521 - werkzeug - INFO -  * Debugger PIN: 672-************-05-08 13:51:52,210 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-05-08 13:51:52,210 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 13:53:06,433 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:53:06] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-08 13:53:53,792 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://***********:5001
2025-05-08 13:53:53,792 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 13:54:21,385 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:54:21] "GET /api/users/health HTTP/1.1" 200 -
2025-05-08 13:54:46,386 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://***********:5002
2025-05-08 13:54:46,386 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 13:55:33,686 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:55:33] "GET /api/students/health HTTP/1.1" 200 -
2025-05-08 13:56:21,348 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://***********:5003
2025-05-08 13:56:21,348 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 13:57:04,684 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 13:57:04] "GET /api/courses/health HTTP/1.1" 200 -
2025-05-08 14:26:32,269 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://***********:5004
2025-05-08 14:26:32,269 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 17:14:22,110 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-05-08 17:14:22,110 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 17:14:42,533 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-05-08 17:14:42,533 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 17:16:21,418 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://***********:5001
2025-05-08 17:16:21,419 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 17:17:12,862 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-05-08 17:17:12,862 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 17:17:22,959 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://***********:5003
2025-05-08 17:17:22,959 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 17:17:57,253 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://***********:5004
2025-05-08 17:17:57,253 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 17:18:26,329 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://***********:5002
2025-05-08 17:18:26,330 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 17:18:52,210 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://***********:5002
2025-05-08 17:18:52,210 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 17:19:11,090 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://***********:5002
2025-05-08 17:19:11,090 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 17:27:58,259 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://***********:5001
2025-05-08 17:27:58,259 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-08 17:29:05,972 - common.middleware - WARNING - User with role None attempted to access /api/users/register which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-08 17:29:05,972 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 17:29:05] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 403 -
2025-05-08 17:29:17,356 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-08 17:29:17,356 - werkzeug - INFO - 127.0.0.1 - - [08/May/2025 17:29:17] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 10:31:01,883 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 10:31:01,883 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 10:31:19,544 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 10:31:19,544 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 10:31:28,207 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 10:31:28,207 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 10:44:31,782 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 10:44:31,783 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 10:44:31] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 10:48:38,595 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 10:48:38,596 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 10:48:38] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 10:50:19,520 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 10:50:19,520 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 10:50:19] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 10:51:14,707 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 10:51:14,707 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 10:52:05,512 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/auth/healt
2025-05-12 10:52:05,512 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 10:52:05] "[31m[1mGET /api/auth/healt HTTP/1.1[0m" 401 -
2025-05-12 11:02:18,971 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 11:02:18,971 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 11:04:41,761 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:04:41] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 11:06:10,824 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 11:06:10,824 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:06:10] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 11:23:47,644 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/register
2025-05-12 11:23:47,645 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:23:47] "[31m[1mPOST /api/users/register?token=eyJhbGciOiJIUzI1NiJ9.e30.KqGpJZaDDx_JllyRLScTgZqWJJUlR2mqXOUFozVwNUA HTTP/1.1[0m" 401 -
2025-05-12 11:35:41,421 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 11:35:41,421 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:35:41] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 11:37:43,642 - common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 11:37:43,646 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:37:43] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 11:38:18,721 - common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 11:38:18,723 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:38:18] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 11:38:55,115 - common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 11:38:55,116 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:38:55] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 11:40:20,335 - common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 11:40:20,336 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:40:20] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 11:40:37,576 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:40:37] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 11:42:16,295 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:42:16] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 11:42:38,529 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/auth/login
2025-05-12 11:42:38,529 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:42:38] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 11:43:46,738 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/health
2025-05-12 11:43:46,738 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:43:46] "[31m[1mGET /api/users/health HTTP/1.1[0m" 401 -
2025-05-12 11:51:39,289 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:51:39] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 11:52:24,516 - common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 11:52:24,517 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:52:24] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 11:59:56,322 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 11:59:56] "[35m[1mPOST /api/auth/login HTTP/1.1[0m" 500 -
2025-05-12 12:00:02,539 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:00:02] "[35m[1mPOST /api/auth/login HTTP/1.1[0m" 500 -
2025-05-12 12:00:43,468 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:00:43] "[35m[1mPOST /api/auth/login HTTP/1.1[0m" 500 -
2025-05-12 12:03:50,443 - common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 12:03:50,444 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:03:50] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 12:04:04,841 - common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 12:04:04,842 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:04:04] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 12:39:31,104 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:39:31] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 12:40:47,276 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:40:47,276 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:40:47] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:41:38,155 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:41:38,155 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:41:38] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:46:31,574 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:46:31,575 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:46:31] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:47:12,465 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:47:12,465 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:47:12] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:47:32,525 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:47:32,525 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:47:32] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:52:35,237 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:52:35,238 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:52:35] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:52:59,322 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:52:59,322 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:52:59] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:53:09,223 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/register
2025-05-12 12:53:09,224 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:53:09] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:54:10,319 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:54:10,319 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:54:10] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:54:21,572 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:54:21,573 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:54:21] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:54:23,039 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:54:23,039 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:54:23] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:54:34,935 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:54:34,939 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:54:34] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:55:03,727 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:55:03,728 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:55:03] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:56:01,427 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/register
2025-05-12 12:56:01,427 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:56:01] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:56:32,103 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/register
2025-05-12 12:56:32,104 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:56:32] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 12:56:55,375 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 12:56:55,375 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 12:56:55] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 13:04:19,330 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:04:19] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 13:05:23,340 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 13:05:23,340 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:05:23] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 13:08:37,693 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 13:08:37,693 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:08:37] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 13:09:13,289 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:09:13] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 13:09:52,075 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 13:09:52,076 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:09:52] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 13:11:14,547 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:11:14] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 13:11:41,317 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 13:11:41,317 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:11:41] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 13:12:17,361 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 13:12:17,361 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:12:17] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 13:16:03,043 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/register
2025-05-12 13:16:03,043 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:16:03] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 13:19:55,235 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:19:55] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 13:20:01,495 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:20:01] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 13:20:14,655 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:20:14] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 13:21:06,189 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 13:21:06,189 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:21:06] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 13:21:17,308 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:21:17] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 13:21:58,155 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 13:21:58,156 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:21:58] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 13:26:24,782 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:26:24] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 13:26:30,910 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:26:30] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 13:26:36,214 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:26:36] "GET /api/students/health HTTP/1.1" 200 -
2025-05-12 13:26:41,267 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:26:41] "GET /api/courses/health HTTP/1.1" 200 -
2025-05-12 13:27:26,601 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:27:26] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 13:27:26,629 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 13:27:26,629 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:27:26] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 13:31:31,218 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:31:31] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 13:31:33,300 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 13:31:33,300 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:31:33] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 13:36:54,424 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 13:36:54,424 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 13:37:07,204 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 13:37:07,204 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 13:37:16,354 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 13:37:16,354 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 13:37:23,249 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 13:37:23,249 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 13:37:47,029 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:37:47] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 13:37:49,099 - common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 13:37:49,099 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 13:37:49] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 14:49:53,601 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 14:49:53,601 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 14:49:59,823 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 14:49:59,823 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 14:50:06,932 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 14:50:06,932 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 14:50:12,497 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 14:50:12,498 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 14:50:50,282 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 14:50:50] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 14:50:52,360 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 14:50:52,361 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 14:50:52] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 15:10:52,376 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:10:52] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 15:10:54,455 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 15:10:54,455 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:10:54] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 15:30:17,464 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 15:30:17,465 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 15:30:17,691 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 15:30:17,691 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 15:30:17,960 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 15:30:17,960 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 15:30:18,174 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 15:30:18,174 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 15:30:18,305 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 15:30:18,305 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 15:31:15,831 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:31:15] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 15:31:17,885 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:31:17] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 15:31:20,151 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:31:20] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 15:31:22,187 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 15:31:22,188 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:31:22] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 15:42:16,048 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 15:42:16,048 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 15:42:21,503 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 15:42:21,503 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 15:42:28,002 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:42:28] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 15:42:30,058 - auth_service.common.middleware - WARNING - Invalid token for path: /api/auth/verify
2025-05-12 15:42:30,059 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:42:30] "[31m[1mGET /api/auth/verify HTTP/1.1[0m" 401 -
2025-05-12 15:42:32,327 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:42:32] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 15:42:34,376 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 15:42:34,377 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:42:34] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 15:44:18,850 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 15:44:18,850 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 15:44:24,182 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 15:44:24,182 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 15:44:31,926 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:44:31] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 15:44:33,974 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:44:33] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 15:44:36,245 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:44:36] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 15:44:38,282 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 15:44:38,283 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:44:38] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 15:45:33,515 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 15:45:33,516 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 15:45:47,070 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:45:47] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 15:45:49,118 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:45:49] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 15:45:51,371 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:45:51] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 15:45:53,415 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 15:45:53,418 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:45:53] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 15:47:41,197 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 15:47:41,198 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 15:58:04,442 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:58:04] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 15:58:10,802 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 15:58:10] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 15:59:50,255 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 15:59:50,255 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:00:06,491 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:00:06] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 16:00:08,540 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:00:08] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 16:00:10,808 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:00:10] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 16:00:12,856 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 16:00:12,857 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:00:12] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 16:01:20,281 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 16:01:20,281 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:01:35,461 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:01:35] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 16:01:37,517 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:01:37] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 16:01:39,797 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:01:39] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 16:01:41,834 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register, error: Signature verification failed
2025-05-12 16:01:41,835 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:01:41] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 16:02:39,794 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 16:02:39,794 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:02:46,709 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:02:46] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 16:02:48,757 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:02:48] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 16:02:51,017 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:02:51] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 16:02:53,049 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register, error: Signature verification failed
2025-05-12 16:02:53,050 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:02:53] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 16:04:10,711 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 16:04:10,712 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:04:20,424 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 16:04:20,424 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:04:32,700 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:04:32] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 16:04:34,749 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:04:34] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 16:04:37,029 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:04:37] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 16:04:39,330 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:04:39] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-12 16:30:42,573 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 16:30:42,574 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:31:02,602 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 16:31:02,602 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:31:39,914 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:31:39] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-12 16:31:41,969 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:31:41] "GET /api/users/health HTTP/1.1" 200 -
2025-05-12 16:31:44,263 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:31:44] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 16:31:46,538 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:31:46] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-12 16:32:37,782 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:32:37] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 16:33:38,260 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:33:38] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-12 16:49:45,523 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 16:49:45,523 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:49:45,523 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:49:45,751 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 16:49:45,751 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:49:45,899 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 16:49:45,899 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:49:45,971 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 16:49:45,971 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:50:05,018 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:50:05] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-12 16:50:05,578 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:50:05] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 16:50:05,923 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-12 16:50:05,925 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:50:05] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-12 16:50:05,924 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-12 16:50:05,925 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:50:05] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-12 16:50:32,482 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-12 16:50:32,482 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:50:32] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-12 16:50:32,796 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-12 16:50:32,797 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:50:32] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-12 16:50:58,735 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/register
2025-05-12 16:50:58,735 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:50:58] "[31m[1mOPTIONS /api/users/register HTTP/1.1[0m" 401 -
2025-05-12 16:57:57,579 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 16:57:57,580 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:57:57,580 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:57:57,796 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 16:57:57,796 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:57:57,970 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 16:57:57,970 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:57:58,192 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 16:57:58,192 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 16:58:43,151 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:58:43] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-12 16:58:43,693 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:58:43] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 16:58:44,024 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-12 16:58:44,025 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:58:44] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-12 16:58:44,026 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-12 16:58:44,026 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:58:44] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-12 16:59:04,178 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses
2025-05-12 16:59:04,179 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:59:04] "[31m[1mOPTIONS /api/courses/courses HTTP/1.1[0m" 401 -
2025-05-12 16:59:39,755 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users
2025-05-12 16:59:39,755 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:59:39] "[31m[1mGET /api/users HTTP/1.1[0m" 401 -
2025-05-12 16:59:53,636 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 16:59:53] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 17:00:17,219 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:00:17] "[33mGET /api/users HTTP/1.1[0m" 404 -
2025-05-12 17:00:33,716 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:00:33] "GET /api/users/users HTTP/1.1" 200 -
2025-05-12 17:07:39,832 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 17:07:39,832 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:07:40,073 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 17:07:40,073 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:07:40,297 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 17:07:40,297 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:07:40,426 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 17:07:40,426 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:07:40,618 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 17:07:40,618 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:29:46,394 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:29:46] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:29:46,396 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:29:46] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:29:46,705 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:29:46,707 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:29:46] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 17:29:46,967 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:29:46,968 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:29:46] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 17:29:54,692 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses
2025-05-12 17:29:54,693 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:29:54] "[31m[1mOPTIONS /api/courses/courses HTTP/1.1[0m" 401 -
2025-05-12 17:29:55,618 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:29:55] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:29:55,622 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:29:55,623 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:29:55] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 17:30:11,135 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:30:11,136 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:30:11] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 17:30:11,232 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /favicon.ico
2025-05-12 17:30:11,233 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:30:11] "[31m[1mGET /favicon.ico HTTP/1.1[0m" 401 -
2025-05-12 17:30:53,334 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:30:53] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:30:53,335 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:30:53] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:30:53,337 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:30:53,337 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:30:53] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 17:30:53,643 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:30:53,643 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:30:53] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 17:31:12,834 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses
2025-05-12 17:31:12,834 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:31:12] "[31m[1mOPTIONS /api/courses/courses HTTP/1.1[0m" 401 -
2025-05-12 17:31:18,134 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:31:18] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:31:18,445 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:31:18,445 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:31:18] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 17:36:16,751 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:36:16] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:36:16,753 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:36:16] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:36:17,059 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:36:17,059 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:36:17] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 17:36:17,323 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:36:17,324 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:36:17] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 17:36:22,445 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses
2025-05-12 17:36:22,445 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:36:22] "[31m[1mOPTIONS /api/courses/courses HTTP/1.1[0m" 401 -
2025-05-12 17:42:17,000 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 17:42:17,000 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:42:17,295 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 17:42:17,295 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:42:17,535 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 17:42:17,535 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:42:17,708 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 17:42:17,708 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:42:17,803 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 17:42:17,803 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:48:24,690 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 17:48:24,690 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:48:24,762 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 17:48:24,762 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:48:25,016 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 17:48:25,016 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:48:25,174 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 17:48:25,174 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:48:25,276 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 17:48:25,276 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:49:00,138 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:49:00] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-12 17:49:02,449 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:49:02] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-12 17:49:19,365 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:49:19] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-12 17:58:39,210 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 17:58:39,210 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:58:39,210 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:58:39,210 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 17:58:39,211 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:58:39,211 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 17:58:39,211 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:58:39,280 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 17:58:39,280 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 17:58:54,173 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:58:54] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:58:54,174 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:58:54] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:58:54,998 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:58:54] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:58:55,302 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:58:55] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 17:59:04,597 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:59:04] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-12 17:59:38,907 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 17:59:38] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:00:04,216 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:00:04] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:00:17,969 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:00:17] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:02:59,996 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 18:02:59,996 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:04:21,143 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 18:04:21,143 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:04:21,327 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:04:21,327 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:04:21,548 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 18:04:21,548 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:04:21,835 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 18:04:21,835 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:04:21,838 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 18:04:21,838 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:04:36,271 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:04:36] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:04:36,836 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:04:36] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:04:37,196 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:04:37] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 18:04:37,201 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:04:37] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 18:04:37,506 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:04:37,507 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:04:37] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:04:37,770 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:04:37,770 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:04:37] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:09:18,211 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:09:18,212 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:10:18,816 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 18:10:18,816 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:10:19,062 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:10:19,063 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:10:19,291 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 18:10:19,292 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:10:19,471 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 18:10:19,472 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:10:19,598 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 18:10:19,598 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:10:28,357 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:10:28] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 18:10:28,361 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:10:28] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-12 18:10:28,666 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:10:28,666 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:10:28] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:10:28,928 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:10:28,928 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:10:28] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:10:42,737 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:10:42] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:10:42,778 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:10:42,778 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:10:42] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:10:43,082 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:10:43,082 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:10:43] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:12:13,704 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:12:13,704 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:12:53,351 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:12:53,351 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:13:30,227 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:13:30,227 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:15:39,162 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:15:39,163 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:16:09,773 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:16:09,774 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:16:09] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:16:10,036 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:16:10,036 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:16:10] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:16:18,851 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:16:18,853 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:16:18] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:16:19,166 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:16:19,166 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:16:19] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:16:27,342 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:16:27,343 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:16:27] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:16:27,603 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:16:27,604 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:16:27] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:16:58,652 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:16:58] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:17:07,046 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:17:07,046 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:17:17,886 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:17:17,887 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:17:17] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:18:36,504 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:18:36,504 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:18:49,524 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:18:49] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:19:12,247 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:19:12,248 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:19:12] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:20:48,479 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:20:48,480 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:21:04,149 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:21:04,150 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:21:04] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:21:16,607 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:21:16] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:21:33,306 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:21:33,307 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:21:33] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:20,999 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 18:22:20,999 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:22:21,312 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:22:21,312 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:22:21,625 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 18:22:21,625 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:22:21,711 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 18:22:21,711 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:22:21,827 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 18:22:21,827 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:22:28,514 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:28,515 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:28] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:28,779 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:28,780 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:28] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:34,415 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:34] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:22:34,440 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:34,440 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:34] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:34,744 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:34,745 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:34] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:38,090 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:38] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-12 18:22:38,413 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:38] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-12 18:22:39,873 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:39,873 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:39] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:41,789 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:41] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-12 18:22:42,620 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:42,621 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:42] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:44,917 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:44,918 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:44] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:45,181 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:45,181 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:45] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:46,099 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:46,100 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:46] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:46,407 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:46,407 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:46] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:47,482 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:47,483 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:47] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:47,747 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:47,748 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:47] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:58,615 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:58] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:22:58,678 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:58,679 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:58] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:22:58,984 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:58,985 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:22:58] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:24:21,823 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:24:21,824 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:24:21] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:24:32,618 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:24:32] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:24:46,414 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:24:46,415 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:24:46] "[31m[1mGET /api/users/users HTTP/1.1[0m" 403 -
2025-05-12 18:25:44,050 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:25:44,050 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:25:47,015 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:25:47] "GET /api/users/users HTTP/1.1" 200 -
2025-05-12 18:25:59,241 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:25:59] "GET /api/users/users HTTP/1.1" 200 -
2025-05-12 18:25:59,497 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:25:59] "GET /api/users/users HTTP/1.1" 200 -
2025-05-12 18:28:07,824 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 18:28:07,824 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:28:08,028 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:28:08,028 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:28:08,306 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 18:28:08,307 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:28:08,572 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 18:28:08,572 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:28:08,622 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 18:28:08,622 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:28:43,602 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:28:43,604 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:28:43] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:29:48,623 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:29:48,624 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:29:48] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:30:28,450 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:30:28] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:31:26,477 - user_service.common.utils - ERROR - Error: Username already exists, Status: 400
2025-05-12 18:31:26,478 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:31:26] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 400 -
2025-05-12 18:31:35,821 - user_service.common.utils - ERROR - Error: Email already exists, Status: 400
2025-05-12 18:31:35,822 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:31:35] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 400 -
2025-05-12 18:31:44,890 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:31:44] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-12 18:32:06,212 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:32:06,213 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:32:06] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:32:13,292 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:32:13,293 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:32:13] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:33:19,245 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:33:19] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:34:55,993 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:34:55,995 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:34:55] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:35:49,051 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:35:49] "GET /api/users/users HTTP/1.1" 200 -
2025-05-12 18:35:49,310 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:35:49] "GET /api/users/users HTTP/1.1" 200 -
2025-05-12 18:35:49,420 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:35:49] "GET /api/users/users HTTP/1.1" 200 -
2025-05-12 18:35:58,044 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:35:58] "GET /api/users/users HTTP/1.1" 200 -
2025-05-12 18:35:58,417 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:35:58] "GET /api/users/users HTTP/1.1" 200 -
2025-05-12 18:35:58,899 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:35:58] "GET /api/users/users HTTP/1.1" 200 -
2025-05-12 18:43:24,637 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 18:43:24,638 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:43:24,850 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:43:24,850 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:43:25,070 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-12 18:43:25,070 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:43:25,226 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-12 18:43:25,226 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:43:25,331 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-12 18:43:25,331 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:43:41,097 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:43:41,098 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:43:41] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:44:20,912 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:44:20,913 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:44:20] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:48:46,835 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 18:48:46,836 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:49:11,918 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:49:11,920 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:49:11] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:49:21,429 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:49:21,430 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:49:21] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:50:38,623 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-12 18:50:38,623 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:50:45,496 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 18:50:45,496 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:50:50,138 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:50:50,139 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:50:50] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:51:49,743 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-12 18:51:49,743 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-12 18:52:00,493 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:52:00,494 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:52:00] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:53:02,607 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:53:02] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 18:53:24,108 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-12 18:53:24,109 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:53:24] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-12 18:55:04,580 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 18:55:04] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-12 19:02:44,550 - werkzeug - INFO - 127.0.0.1 - - [12/May/2025 19:02:44] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 10:23:27,570 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 10:23:27] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-13 10:23:28,259 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 10:23:28] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 10:23:59,650 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 10:23:59] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 10:23:59,985 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 10:23:59,985 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 10:23:59] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 10:23:59,986 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 10:23:59,987 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 10:23:59] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 10:46:02,147 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 10:46:02,147 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 10:46:02,148 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 10:46:02,148 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 10:46:21,102 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 10:46:21] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 10:48:56,349 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 10:48:56] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 10:56:39,428 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-13 10:56:39,430 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 10:56:39] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-13 10:57:35,535 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-13 10:57:35,536 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 10:57:35] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-13 10:59:05,979 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 10:59:05] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 11:00:21,836 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:00:21] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 11:01:22,759 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:01:22] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 11:01:23,109 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 11:01:23,109 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:01:23] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 11:01:23,109 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 11:01:23,110 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:01:23] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 11:03:51,824 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:03:51] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 11:09:03,927 - auth_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/auth/healt
2025-05-13 11:09:03,928 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:09:03] "[31m[1mGET /api/auth/healt HTTP/1.1[0m" 401 -
2025-05-13 11:09:11,615 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:09:11] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-13 11:13:06,015 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users/{7}
2025-05-13 11:13:06,015 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:13:06] "[31m[1mGET /api/users/users/{7} HTTP/1.1[0m" 401 -
2025-05-13 11:13:16,696 - common.middleware - WARNING - User with role Teacher attempted to access /api/users/users/7 which requires roles ['Super Admin', 'Admin']
2025-05-13 11:13:16,696 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:13:16] "[31m[1mGET /api/users/users/7 HTTP/1.1[0m" 403 -
2025-05-13 11:13:28,118 - common.middleware - WARNING - User with role Teacher attempted to access /api/users/users/{7} which requires roles ['Super Admin', 'Admin']
2025-05-13 11:13:28,118 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:13:28] "[31m[1mGET /api/users/users/{7} HTTP/1.1[0m" 403 -
2025-05-13 11:13:40,479 - common.middleware - WARNING - User with role Teacher attempted to access /api/users/users/{user_id = 7} which requires roles ['Super Admin', 'Admin']
2025-05-13 11:13:40,479 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:13:40] "[31m[1mGET /api/users/users/{user_id%20=%207} HTTP/1.1[0m" 403 -
2025-05-13 11:14:05,332 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:14:05] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 11:14:16,931 - common.middleware - WARNING - Invalid token for path: /api/users/users/{user_id = 7}
2025-05-13 11:14:16,932 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:14:16] "[31m[1mGET /api/users/users/{user_id%20=%207} HTTP/1.1[0m" 401 -
2025-05-13 11:15:03,340 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:15:03] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 11:15:27,312 - common.middleware - WARNING - User with role Teacher attempted to access /api/users/users/{user_id = 7} which requires roles ['Super Admin', 'Admin']
2025-05-13 11:15:27,312 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:15:27] "[31m[1mGET /api/users/users/{user_id%20=%207} HTTP/1.1[0m" 403 -
2025-05-13 11:15:37,076 - common.middleware - WARNING - User with role Teacher attempted to access /api/users/users/{7} which requires roles ['Super Admin', 'Admin']
2025-05-13 11:15:37,076 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:15:37] "[31m[1mGET /api/users/users/{7} HTTP/1.1[0m" 403 -
2025-05-13 11:17:28,578 - common.middleware - WARNING - User with role Teacher attempted to access /api/users/users/7 which requires roles ['Super Admin', 'Admin']
2025-05-13 11:17:28,578 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:17:28] "[31m[1mGET /api/users/users/7 HTTP/1.1[0m" 403 -
2025-05-13 11:18:22,461 - common.middleware - WARNING - User with role Teacher attempted to access /api/users/users/7 which requires roles ['Super Admin', 'Admin']
2025-05-13 11:18:22,461 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:18:22] "[31m[1mGET /api/users/users/7 HTTP/1.1[0m" 403 -
2025-05-13 11:18:53,619 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:18:53] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 11:19:06,373 - common.middleware - WARNING - User with role Teacher attempted to access /api/users/users/7 which requires roles ['Super Admin', 'Admin']
2025-05-13 11:19:06,373 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:19:06] "[31m[1mGET /api/users/users/7 HTTP/1.1[0m" 403 -
2025-05-13 11:19:49,384 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:19:49] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 11:23:36,273 - common.middleware - WARNING - User with role Teacher attempted to access /api/users/users/7 which requires roles ['Super Admin', 'Admin']
2025-05-13 11:23:36,273 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:23:36] "[31m[1mGET /api/users/users/7 HTTP/1.1[0m" 403 -
2025-05-13 11:23:46,639 - common.middleware - WARNING - User with role Teacher attempted to access /api/users/users/1 which requires roles ['Super Admin', 'Admin']
2025-05-13 11:23:46,639 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:23:46] "[31m[1mGET /api/users/users/1 HTTP/1.1[0m" 403 -
2025-05-13 11:23:57,389 - common.middleware - WARNING - User with role Teacher attempted to access /api/users/users/5 which requires roles ['Super Admin', 'Admin']
2025-05-13 11:23:57,390 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:23:57] "[31m[1mGET /api/users/users/5 HTTP/1.1[0m" 403 -
2025-05-13 11:28:36,905 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:28:36,914 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:28:36] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:29:01,259 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:29:01,259 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:29:01] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:29:31,785 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:29:31,786 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:29:31] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:33:22,763 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:33:22,763 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:33:22] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:33:28,312 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:33:28,344 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:33:28] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:34:30,953 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:34:30,954 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:34:30] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:34:32,074 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:34:32,074 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:34:32] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:34:32,744 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:34:32,744 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:34:32] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:34:33,303 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:34:33,304 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:34:33] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:34:34,470 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:34:34,470 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:34:34] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:34:36,692 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:34:36,692 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:34:36] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:37:45,934 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:37:45,935 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:37:45] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:38:00,449 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:38:00,450 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:38:00] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:39:11,408 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:39:11,409 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:39:11] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:43:49,180 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:43:49] "GET /api/users/health HTTP/1.1" 200 -
2025-05-13 11:45:23,523 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:45:23] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 11:46:18,036 - student_service.common.middleware - WARNING - Invalid token for path: /register
2025-05-13 11:46:18,037 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:46:18] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:46:20,629 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:46:20] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 11:46:30,431 - common.middleware - WARNING - Invalid token for path: /api/users/users/5
2025-05-13 11:46:30,431 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:46:30] "[31m[1mGET /api/users/users/5 HTTP/1.1[0m" 401 -
2025-05-13 11:46:52,507 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:46:52] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 11:47:08,601 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:47:08] "GET /api/users/users/5 HTTP/1.1" 200 -
2025-05-13 11:47:56,113 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:47:56,113 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:47:56] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:49:39,658 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:49:39,659 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:49:39] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:50:06,205 - student_service.common.middleware - WARNING - Invalid token for path: /api/students/students
2025-05-13 11:50:06,236 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:50:06] "[31m[1mPOST /api/students/students HTTP/1.1[0m" 401 -
2025-05-13 11:50:15,219 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:50:15] "GET /api/users/users/5 HTTP/1.1" 200 -
2025-05-13 11:51:02,437 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:51:02] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 11:51:12,490 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 11:51:12] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 201 -
2025-05-13 11:54:27,411 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:54:27,412 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:54:27] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 11:54:46,435 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /register
2025-05-13 11:54:46,435 - werkzeug - INFO - 192.168.1.10 - - [13/May/2025 11:54:46] "[31m[1mPOST /register HTTP/1.1[0m" 401 -
2025-05-13 12:10:39,733 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 12:10:39] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 12:11:06,972 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 12:11:06] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 12:11:08,463 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 12:11:08] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 12:11:37,090 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 12:11:37] "GET /api/students/students/1 HTTP/1.1" 200 -
2025-05-13 13:22:18,791 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 13:22:18,794 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 13:22:18] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 14:39:21,949 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 14:39:21,950 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 14:39:21,950 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-13 14:39:21,950 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 14:39:21,950 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 14:39:21,950 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 14:39:21,953 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 14:39:21,953 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 14:39:21,988 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 14:39:21,988 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 14:39:29,952 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 14:39:29] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 14:40:06,098 - user_service.common.utils - ERROR - Error: You don't have permission to register a Student, Status: 403
2025-05-13 14:40:06,099 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 14:40:06] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 403 -
2025-05-13 14:40:29,716 - user_service.common.utils - ERROR - Error: You don't have permission to register a Teacher, Status: 403
2025-05-13 14:40:29,716 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 14:40:29] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 403 -
2025-05-13 14:54:04,978 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 14:54:04,978 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 14:54:05,212 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 14:54:05,212 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 14:54:05,413 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-13 14:54:05,413 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 14:54:05,640 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 14:54:05,640 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 14:54:05,751 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 14:54:05,751 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 14:54:48,299 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 14:54:48] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 14:55:20,690 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 14:55:20] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 14:56:24,455 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 14:56:24] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 14:58:37,589 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 14:58:37] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 14:59:03,516 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 14:59:03] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 15:01:58,697 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 15:01:58,697 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:01:58] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 15:01:58,697 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 15:01:58,698 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:01:58] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 15:02:03,182 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:02:03] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-13 15:02:03,790 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-13 15:02:03,792 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:02:03] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-13 15:02:15,168 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:02:15] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 15:02:15,231 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 15:02:15,231 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:02:15] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 15:02:15,533 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 15:02:15,533 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:02:15] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 15:02:32,194 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 15:02:32,194 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 15:02:32,195 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:02:32] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 15:02:32,195 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:02:32] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 15:07:40,039 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:07:40] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 201 -
2025-05-13 15:13:51,636 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:13:51] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 15:15:52,809 - parent_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/parents/parents
2025-05-13 15:15:52,810 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:15:52] "[31m[1mPOST /api/parents/parents HTTP/1.1[0m" 401 -
2025-05-13 15:16:27,984 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:16:27] "[35m[1mPOST /api/parents/parents HTTP/1.1[0m" 201 -
2025-05-13 15:18:07,343 - student_service.common.utils - ERROR - Error: Student not found, Status: 404
2025-05-13 15:18:07,347 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:18:07] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:23:19,473 - student_service.common.utils - ERROR - Error: Student not found, Status: 404
2025-05-13 15:23:19,474 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:23:19] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:31:40,464 - student_service.common.utils - ERROR - Error: Student not found, Status: 404
2025-05-13 15:31:40,465 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:31:40] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:32:52,208 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:32:52] "[31m[1mGET /api/parents/1 HTTP/1.1[0m" 405 -
2025-05-13 15:32:52,209 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:32:52,210 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:32:52] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:34:45,318 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-13 15:34:45,318 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 15:34:45,536 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 15:34:45,536 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 15:34:45,831 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 15:34:45,832 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 15:34:46,029 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 15:34:46,029 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 15:34:46,177 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 15:34:46,177 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 15:35:35,646 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:35:35] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 15:35:51,485 - student_service.common.middleware - WARNING - Invalid token for path: /api/students/map-parent
2025-05-13 15:35:51,485 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:35:51] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:36:29,144 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:36:29] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 15:38:42,118 - student_service.common.middleware - WARNING - Invalid token for path: /api/students/map-parent
2025-05-13 15:38:42,118 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:38:42] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:45:02,805 - student_service.common.middleware - WARNING - Invalid token for path: /api/students/map-parent
2025-05-13 15:45:02,805 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:45:02] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:47:43,746 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:47:43] "[31m[1mGET /api/parents/1 HTTP/1.1[0m" 405 -
2025-05-13 15:47:43,746 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:47:43,748 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:47:43] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:48:29,302 - student_service.common.utils - ERROR - Error: Student with this user_id already exists, Status: 400
2025-05-13 15:48:29,303 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:48:29] "[31m[1mPOST /api/students/students HTTP/1.1[0m" 400 -
2025-05-13 15:48:31,362 - parent_service.common.utils - ERROR - Error: Parent with this user_id already exists, Status: 400
2025-05-13 15:48:31,363 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:48:31] "[31m[1mPOST /api/parents/parents HTTP/1.1[0m" 400 -
2025-05-13 15:48:59,863 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:48:59] "[31m[1mGET /api/students/user/13 HTTP/1.1[0m" 405 -
2025-05-13 15:49:01,915 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:49:01] "[31m[1mGET /api/parents/user/14 HTTP/1.1[0m" 405 -
2025-05-13 15:50:31,583 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:31] "[31m[1mGET /api/parents/1 HTTP/1.1[0m" 405 -
2025-05-13 15:50:31,583 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:50:31,584 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:31] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:50:35,681 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:35] "[31m[1mGET /api/parents/1 HTTP/1.1[0m" 405 -
2025-05-13 15:50:35,682 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:50:35,683 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:35] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:50:37,733 - student_service.common.utils - ERROR - Error: Student not found, Status: 404
2025-05-13 15:50:37,734 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:37] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:50:41,833 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:41] "[31m[1mGET /api/parents/2 HTTP/1.1[0m" 405 -
2025-05-13 15:50:41,834 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:50:41,834 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:41] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:50:45,932 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:45] "[31m[1mGET /api/parents/2 HTTP/1.1[0m" 405 -
2025-05-13 15:50:45,932 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:50:45,933 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:45] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:50:47,978 - student_service.common.utils - ERROR - Error: Student not found, Status: 404
2025-05-13 15:50:47,979 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:47] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:50:52,065 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:52] "[31m[1mGET /api/parents/3 HTTP/1.1[0m" 405 -
2025-05-13 15:50:52,066 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:50:52,067 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:52] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:50:52,940 - student_service.common.middleware - WARNING - Invalid token for path: /api/students/map-parent
2025-05-13 15:50:52,941 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:52] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:50:56,172 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:56] "[31m[1mGET /api/parents/3 HTTP/1.1[0m" 405 -
2025-05-13 15:50:56,173 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:50:56,175 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:56] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:50:58,226 - student_service.common.utils - ERROR - Error: Student not found, Status: 404
2025-05-13 15:50:58,227 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:50:58] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:51:57,330 - student_service.common.utils - ERROR - Error: Student with this user_id already exists, Status: 400
2025-05-13 15:51:57,331 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:51:57] "[31m[1mPOST /api/students/students HTTP/1.1[0m" 400 -
2025-05-13 15:51:59,384 - parent_service.common.utils - ERROR - Error: Parent with this user_id already exists, Status: 400
2025-05-13 15:51:59,385 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:51:59] "[31m[1mPOST /api/parents/parents HTTP/1.1[0m" 400 -
2025-05-13 15:52:03,475 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:52:03] "[31m[1mGET /api/parents/1 HTTP/1.1[0m" 405 -
2025-05-13 15:52:03,476 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:52:03,477 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:52:03] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:52:45,862 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:52:45] "[31m[1mGET /api/parents/14 HTTP/1.1[0m" 405 -
2025-05-13 15:52:45,863 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:52:45,864 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:52:45] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:53:25,274 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:53:25] "[31m[1mGET /api/parents/14 HTTP/1.1[0m" 405 -
2025-05-13 15:53:25,274 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:53:25,275 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:53:25] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:54:02,159 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:54:02] "[31m[1mGET /api/parents/14 HTTP/1.1[0m" 405 -
2025-05-13 15:54:02,160 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:54:02,161 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:54:02] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:54:48,392 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:54:48,392 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:54:48] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:54:50,431 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:54:50,432 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:54:50] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:54:52,470 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:54:52,471 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:54:52] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:54:54,520 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:54:54,520 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:54:54] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:54:56,564 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:54:56,564 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:54:56] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:54:58,610 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:54:58,610 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:54:58] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:00,667 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:00,667 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:00] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:02,715 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:02,715 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:02] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:04,763 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:04,764 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:04] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:06,816 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:06,817 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:06] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:08,863 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:08,863 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:08] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:10,910 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:10,911 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:10] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:19,998 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 15:55:19,998 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 15:55:27,541 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:27,542 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:27] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:29,592 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:29,593 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:29] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:31,642 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:31,642 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:31] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:33,689 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:33,690 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:33] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:35,742 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:35,742 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:35] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:37,794 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:37,794 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:37] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:39,847 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:39,847 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:39] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:41,904 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:41,905 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:41] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:43,954 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:43,955 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:43] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:46,008 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:46,009 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:46] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:48,054 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:48,054 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:48] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:55:50,090 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-13 15:55:50,091 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:55:50] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 15:57:37,760 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:57:37] "[31m[1mGET /api/parents/1 HTTP/1.1[0m" 405 -
2025-05-13 15:57:37,761 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-13 15:57:37,762 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:57:37] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-13 15:58:21,993 - student_service.common.middleware - WARNING - Invalid token for path: /api/students/map-parent
2025-05-13 15:58:21,993 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 15:58:21] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 16:00:05,691 - parent_service.common.utils - ERROR - Error: Parent with this user_id already exists, Status: 400
2025-05-13 16:00:05,692 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:00:05] "[31m[1mPOST /api/parents/parents HTTP/1.1[0m" 400 -
2025-05-13 16:02:08,495 - student_service.common.middleware - WARNING - Invalid token for path: /api/students/map-parent
2025-05-13 16:02:08,495 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:02:08] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-13 16:03:28,571 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-13 16:03:28,571 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:03:28,785 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 16:03:28,785 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:03:29,025 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 16:03:29,026 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:03:29,205 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 16:03:29,205 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:03:29,339 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 16:03:29,339 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:03:38,502 - student_service.common.utils - ERROR - Error: Error mapping parent to student: (psycopg2.errors.ForeignKeyViolation) insert or update on table "parent_students" violates foreign key constraint "parent_students_student_id_fkey"
DETAIL:  Key (student_id)=(13) is not present in table "students".

[SQL: INSERT INTO parent_students (parent_id, student_id, relationship, created_at) VALUES (%(parent_id)s, %(student_id)s, %(relationship)s, %(created_at)s) RETURNING parent_students.id]
[parameters: {'parent_id': 14, 'student_id': 13, 'relationship': 'Mother', 'created_at': datetime.datetime(2025, 5, 13, 10, 33, 38, 492956)}]
(Background on this error at: https://sqlalche.me/e/14/gkpj), Status: 500
2025-05-13 16:03:38,502 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:03:38] "[35m[1mPOST /api/students/map-parent HTTP/1.1[0m" 500 -
2025-05-13 16:04:44,398 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:04:44] "[35m[1mPOST /api/students/map-parent HTTP/1.1[0m" 201 -
2025-05-13 16:10:46,456 - course_service.common.utils - ERROR - Error: You don't have permission to create a course, Status: 403
2025-05-13 16:10:46,461 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:10:46] "[31m[1mPOST /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 16:11:55,287 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:11:55] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 16:12:48,654 - course_service.common.utils - ERROR - Error: You don't have permission to create a course, Status: 403
2025-05-13 16:12:48,655 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:12:48] "[31m[1mPOST /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 16:13:45,781 - course_service.common.utils - ERROR - Error: You don't have permission to create a course, Status: 403
2025-05-13 16:13:45,781 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:13:45] "[31m[1mPOST /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 16:15:04,683 - course_service.common.utils - ERROR - Error: You don't have permission to create a course, Status: 403
2025-05-13 16:15:04,684 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:15:04] "[31m[1mPOST /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 16:15:12,207 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:15:12] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 16:15:31,450 - course_service.common.utils - ERROR - Error: You don't have permission to create a course, Status: 403
2025-05-13 16:15:31,451 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:15:31] "[31m[1mPOST /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 16:19:45,157 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 16:19:45,158 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:20:18,768 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:20:18] "[35m[1mPOST /api/courses/courses HTTP/1.1[0m" 201 -
2025-05-13 16:21:17,858 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:21:17] "[35m[1mPOST /api/courses/courses HTTP/1.1[0m" 201 -
2025-05-13 16:24:12,268 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses/1
2025-05-13 16:24:12,268 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:24:12] "[31m[1mGET /api/courses/courses/1 HTTP/1.1[0m" 401 -
2025-05-13 16:24:19,751 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:24:19] "GET /api/courses/courses/1 HTTP/1.1" 200 -
2025-05-13 16:25:41,548 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/map-student
2025-05-13 16:25:41,549 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:25:41] "[31m[1mPOST /api/courses/map-student HTTP/1.1[0m" 401 -
2025-05-13 16:25:52,473 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:25:52] "[31m[1mGET /api/students/13 HTTP/1.1[0m" 405 -
2025-05-13 16:25:52,474 - course_service.common.utils - ERROR - Error: Student not found or service unavailable, Status: 404
2025-05-13 16:25:52,475 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:25:52] "[33mPOST /api/courses/map-student HTTP/1.1[0m" 404 -
2025-05-13 16:26:26,557 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:26:26] "[31m[1mGET /api/students/2 HTTP/1.1[0m" 405 -
2025-05-13 16:26:26,557 - course_service.common.utils - ERROR - Error: Student not found or service unavailable, Status: 404
2025-05-13 16:26:26,559 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:26:26] "[33mPOST /api/courses/map-student HTTP/1.1[0m" 404 -
2025-05-13 16:28:15,384 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:28:15] "[31m[1mGET /api/students/2 HTTP/1.1[0m" 405 -
2025-05-13 16:28:15,385 - course_service.common.utils - ERROR - Error: Student not found or service unavailable, Status: 404
2025-05-13 16:28:15,386 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:28:15] "[33mPOST /api/courses/map-student HTTP/1.1[0m" 404 -
2025-05-13 16:32:21,747 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 16:32:21,747 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:32:49,497 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:32:49] "[35m[1mPOST /api/courses/map-student HTTP/1.1[0m" 201 -
2025-05-13 16:36:43,723 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:36:43] "[35m[1mPOST /api/courses/map-student HTTP/1.1[0m" 201 -
2025-05-13 16:40:22,932 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/student-courses/1
2025-05-13 16:40:22,933 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:40:22] "[31m[1mGET /api/courses/student-courses/1 HTTP/1.1[0m" 401 -
2025-05-13 16:40:29,882 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:40:29] "GET /api/courses/student-courses/1 HTTP/1.1" 200 -
2025-05-13 16:52:18,448 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-13 16:52:18,449 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:52:18,657 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 16:52:18,657 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:52:18,870 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 16:52:18,870 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:52:19,030 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 16:52:19,030 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:52:19,129 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 16:52:19,129 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:52:37,052 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:52:37] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-13 16:52:37,584 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:52:37] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 16:52:37,913 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 16:52:37,913 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:52:37] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 16:52:37,913 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 16:52:37,914 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:52:37] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 16:54:15,077 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 16:54:15,078 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:54:25,805 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-13 16:54:25,805 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:54:35,921 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 16:54:35,921 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:54:49,481 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 16:54:49,481 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:54:53,343 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 16:54:53,343 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 16:55:14,258 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:55:14] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 16:55:44,094 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 16:55:44,094 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:55:44] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 16:55:44,095 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 16:55:44,095 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 16:55:44] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:00:29,599 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:00:29,599 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:00:29] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:00:29,602 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:00:29,603 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:00:29] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:01:28,288 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:01:28,289 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:01:28] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:01:28,599 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:01:28,600 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:01:28] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:02:49,099 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:02:49,100 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:02:49] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:02:49,101 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:02:49,101 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:02:49] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:05:43,809 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:05:43,810 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:05:43] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:05:43,810 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:05:43,810 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:05:43] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:06:08,388 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:06:08] "OPTIONS /api/users/register HTTP/1.1" 200 -
2025-05-13 17:06:08,782 - user_service.common.utils - ERROR - Error: Username already exists, Status: 400
2025-05-13 17:06:08,784 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:06:08] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 400 -
2025-05-13 17:10:57,339 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:10:57,340 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:10:57] "[31m[1mGET /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:10:57,426 - common.middleware - WARNING - Missing or invalid Authorization header for path: /favicon.ico
2025-05-13 17:10:57,427 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:10:57] "[31m[1mGET /favicon.ico HTTP/1.1[0m" 401 -
2025-05-13 17:11:23,765 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:11:23,765 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:11:23] "[31m[1mGET /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:11:24,070 - common.middleware - WARNING - Missing or invalid Authorization header for path: /favicon.ico
2025-05-13 17:11:24,071 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:11:24] "[31m[1mGET /favicon.ico HTTP/1.1[0m" 401 -
2025-05-13 17:11:36,753 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:11:36] "[31m[1mGET /api/users/register HTTP/1.1[0m" 405 -
2025-05-13 17:11:36,839 - common.middleware - WARNING - Missing or invalid Authorization header for path: /favicon.ico
2025-05-13 17:11:36,839 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:11:36] "[31m[1mGET /favicon.ico HTTP/1.1[0m" 401 -
2025-05-13 17:17:06,276 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:17:06,277 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:17:06] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:17:06,278 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:17:06,279 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:17:06] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:17:08,574 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:17:08] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 17:17:08,892 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:17:08] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 17:17:21,062 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:17:21,062 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:17:21] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:17:24,424 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:17:24] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 17:17:24,431 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:17:24] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 17:17:40,470 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:17:40,470 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:17:40] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:17:41,982 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:17:41] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 17:17:42,300 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:17:42] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 17:19:58,634 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:19:58,634 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:19:58] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:24:16,832 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-13 17:24:16,832 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 17:24:16,893 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 17:24:16,893 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 17:24:17,150 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 17:24:17,150 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 17:24:17,303 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 17:24:17,303 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 17:24:17,359 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 17:24:17,359 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 17:24:20,766 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:24:20,766 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:24:20] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:24:20,766 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:24:20,767 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:24:20] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:25:20,874 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:25:20] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 17:27:20,075 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:27:20] "OPTIONS /api/users/register HTTP/1.1" 200 -
2025-05-13 17:27:35,406 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:27:35] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 17:27:35,737 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:27:35] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 17:28:36,402 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:28:36] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 17:28:36,762 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:28:36] "[35m[1mPOST /api/courses/courses HTTP/1.1[0m" 201 -
2025-05-13 17:28:36,982 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:28:36] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 17:36:26,683 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:36:26,684 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:36:26] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:36:26,685 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:36:26,685 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:36:26] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:36:38,449 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:36:38] "OPTIONS /api/users/register HTTP/1.1" 200 -
2025-05-13 17:42:23,705 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 17:42:23,705 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 17:42:57,010 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:42:57] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 17:42:59,341 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:42:59] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 17:43:11,988 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:43:11,988 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:43:11] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:43:11,989 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-13 17:43:11,989 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:43:11] "[31m[1mOPTIONS /api/users/users HTTP/1.1[0m" 401 -
2025-05-13 17:45:02,528 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-13 17:45:02,529 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 17:45:02,728 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 17:45:02,729 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 17:45:02,936 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 17:45:02,937 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 17:45:03,134 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 17:45:03,134 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 17:45:03,259 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 17:45:03,259 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 17:45:08,045 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:45:08] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-13 17:45:08,046 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:45:08] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-13 17:45:08,390 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:45:08] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 17:45:08,691 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 17:45:08] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:09:09,867 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:09:09] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:09:10,199 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:09:10] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:09:11,433 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:09:11] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:09:12,032 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:09:12] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:09:14,891 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:09:14] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:09:15,266 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:09:15] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:09:18,106 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:09:18] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:10:40,371 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:10:40] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:10:40,686 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:10:40] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:10:41,453 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:10:41] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:10:43,449 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:10:43] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:10:50,046 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:10:50] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:11:10,089 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:10] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:11:10,366 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:10] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:11:12,818 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:12] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:11:13,174 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:13] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:11:14,326 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:14] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:11:14,583 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:14] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:11:28,013 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:28] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:11:28,329 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:28] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:11:46,108 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:46] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:11:49,822 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:49] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:11:49,826 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:49] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:11:52,281 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:11:52] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:12:00,724 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:12:00] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:12:01,050 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-13 18:12:01,052 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:12:01] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-13 18:12:10,357 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:12:10] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:12:12,016 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:12:12] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:12:16,565 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:12:16] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:12:31,417 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:12:31] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:12:31,733 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:12:31] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:12:36,274 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:12:36] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:12:41,115 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:12:41] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:12:46,888 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:12:46] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:12:46,889 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:12:46] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:13:07,032 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:13:07] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:13:07,560 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:13:07] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:13:27,344 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:13:27] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:13:36,491 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:13:36] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:13:36,806 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:13:36] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:13:37,920 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:13:37] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:13:37,923 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:13:37] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:14:03,419 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:14:03] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:14:04,808 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:14:04] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:14:25,128 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:14:25] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:14:28,457 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:14:28] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:14:39,584 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:14:39] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:15:33,007 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-13 18:15:33,008 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:15:33,206 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 18:15:33,206 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:15:33,407 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 18:15:33,407 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:15:33,593 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 18:15:33,593 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:15:33,720 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 18:15:33,720 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:15:37,066 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:15:37] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:15:37,429 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:15:37] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:15:37,735 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:15:37] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:20:26,516 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-13 18:20:26,519 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:20:26] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-13 18:20:38,449 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-13 18:20:38,450 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:20:38] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-13 18:21:11,873 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:11] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:21:12,219 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:12] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:21:12,221 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:12] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:21:12,527 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:21:12,531 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:12] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:21:12,790 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:21:12,791 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:12] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:21:12,838 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:21:12,839 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:12] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:21:29,465 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:29] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:21:29,467 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:21:29,468 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:29] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:21:29,777 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:29] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:21:30,082 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:21:30,082 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:30] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:21:31,226 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:21:31,227 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:31] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:21:31,532 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:21:31,533 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:31] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:21:31,843 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:21:31,843 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:21:31] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:32:21,157 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:21] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:32:21,159 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:21] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:32:21,467 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:32:21,467 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:21] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:32:21,733 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:32:21,733 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:21] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:32:21,779 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:32:21,779 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:21] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:32:29,222 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:29] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:32:29,230 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:32:29,230 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:29] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:32:29,531 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:29] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:32:29,843 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:32:29,843 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:29] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:32:44,559 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:44] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:32:44,974 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:44] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:32:45,235 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:32:45] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:33:13,286 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 18:33:13,287 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:33:34,601 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:33:34] "OPTIONS /api/users/register HTTP/1.1" 200 -
2025-05-13 18:33:35,307 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:33:35] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 18:33:35,367 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:33:35] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:33:57,702 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:33:57] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:33:58,030 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:33:58] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:33:58,031 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:33:58] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:33:58,337 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:33:58,338 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:33:58] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:33:58,602 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:33:58,602 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:33:58] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:34:35,750 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:34:35,750 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:34:35] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:34:37,799 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:34:37] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:34:54,503 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 18:34:54,503 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:35:00,823 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:35:00,823 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:35:00] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:35:02,875 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:35:02] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:35:17,789 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:35:17] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:35:17,790 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:35:17] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:35:18,098 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:35:18,099 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:35:18] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:35:18,362 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:35:18,363 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:35:18] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:35:39,159 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 18:35:39,159 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:35:46,017 - student_service.common.middleware - WARNING - User with role Student attempted to access /api/students/students which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-13 18:35:46,017 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:35:46] "[31m[1mGET /api/students/students HTTP/1.1[0m" 403 -
2025-05-13 18:35:48,069 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:35:48] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:36:19,731 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-13 18:36:19,733 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:36:19] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-13 18:36:32,691 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-13 18:36:32,692 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:36:32] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-13 18:36:37,875 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-13 18:36:37,876 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:36:37] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-13 18:37:04,307 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 18:37:04,307 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:37:16,726 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:37:16] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:37:17,153 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:37:17] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:37:17,420 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:37:17] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:37:26,165 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:37:26] "[31m[1mGET /api/students/student-profile HTTP/1.1[0m" 405 -
2025-05-13 18:37:46,256 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 18:37:46,256 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:37:49,667 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:37:49] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:37:50,035 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:37:50] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:37:51,933 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:37:51] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:37:53,433 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:37:53] "[31m[1mGET /api/students/student-profile HTTP/1.1[0m" 405 -
2025-05-13 18:37:56,739 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:37:56] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:37:58,023 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:37:58] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:37:59,174 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:37:59] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:39:19,708 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:39:19] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:39:20,422 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 18:39:20,422 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:39:51,231 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:39:51] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:39:56,545 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:39:56] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:40:10,075 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:40:10] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:40:29,995 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:40:29] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:40:39,135 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:40:39] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:40:40,435 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:40:40] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:40:40,441 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:40:40] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:41:00,428 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:41:00] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:41:00,430 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:41:00] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:41:00,431 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:41:00] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:41:00,432 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:41:00] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:41:10,765 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:41:10] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:41:15,621 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:41:15] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-13 18:42:26,580 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-13 18:42:26,580 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:42:26,836 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 18:42:26,836 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:42:27,072 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-13 18:42:27,072 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:42:27,356 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 18:42:27,356 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:42:27,366 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 18:42:27,367 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 18:42:32,270 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:42:32] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:42:32,620 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:42:32] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:42:32,933 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:42:32] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:42:35,224 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:42:35] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:42:35,546 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:42:35] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:42:38,743 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:42:38] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:43:56,742 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:43:56] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 18:43:56,794 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:43:56] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:44:04,955 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:04] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:44:05,286 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:05] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-13 18:44:05,287 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:05] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-13 18:44:05,547 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:05] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:05,610 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:05] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:05,872 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:05] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:06,127 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:06] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:06,187 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:06] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:06,432 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:06] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:10,904 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:10] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-13 18:44:10,921 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:10] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:11,211 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:11] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-13 18:44:11,233 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:11] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:11,487 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:11] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:11,548 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:11] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:12,684 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:12] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-13 18:44:12,685 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:12] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-13 18:44:12,691 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:12] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:13,009 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:13] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:13,323 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:13] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:13,630 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:13] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:15,021 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:15] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-13 18:44:15,337 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:15] "[31m[1mOPTIONS /api/students/student-profile HTTP/1.1[0m" 400 -
2025-05-13 18:44:15,342 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:15] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:15,599 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:15] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:15,663 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:15] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:15,911 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:15] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:44:49,746 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-13 18:44:49,748 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:49] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-13 18:44:55,537 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-13 18:44:55,538 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:44:55] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-13 18:45:05,437 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:05] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:45:05,500 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:05] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:45:05,817 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:05] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:45:18,307 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:18] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:45:18,310 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:18] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:45:18,616 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 18:45:18,619 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:18] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 18:45:20,679 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:20] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-13 18:45:21,001 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:21] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-13 18:45:21,595 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:21] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-13 18:45:21,598 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:21] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:45:24,044 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:24] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-13 18:45:49,765 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:49] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:45:50,093 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:50] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-13 18:45:50,094 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:50] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-13 18:45:50,455 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:50] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:45:50,733 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:45:50] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:46:20,953 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:46:20] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-13 18:46:21,320 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:46:21] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:46:37,841 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:46:37] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:46:38,177 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:46:38] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:46:38,442 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:46:38] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:46:57,329 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:46:57] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:46:57,643 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:46:57] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:46:57,952 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 18:46:57,952 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:46:57] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 18:47:00,745 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:00] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:47:01,635 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 18:47:01,635 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:01] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 18:47:01,638 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:01] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:47:03,230 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:03] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-13 18:47:03,540 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:03] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-13 18:47:04,561 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:04] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-13 18:47:04,879 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:04] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:47:06,447 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:06] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:47:11,087 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:11] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:47:11,393 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 18:47:11,393 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:11] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 18:47:11,394 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:11] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:47:17,422 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:17] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:47:20,492 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:20] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:47:20,494 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:20] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:47:20,495 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 18:47:20,496 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:20] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 18:47:22,094 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:47:22] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 18:48:07,671 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:48:07] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 18:48:08,053 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:48:08] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:48:08,307 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:48:08] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:48:28,330 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:48:28] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:48:28,643 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:48:28] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:48:48,786 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:48:48] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 18:49:09,782 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:49:09] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:49:09,789 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:49:09] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 18:49:13,010 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 18:49:13] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 19:04:41,431 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-13 19:04:41,432 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 19:04:41,432 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 19:04:41,445 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-13 19:04:41,445 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 19:04:41,450 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-13 19:04:41,450 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 19:04:41,510 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-13 19:04:41,511 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-13 19:05:00,489 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:00] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-13 19:05:01,164 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:01] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 19:05:01,535 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:01] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-13 19:05:01,536 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:01] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-13 19:05:01,856 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:01] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 19:05:02,175 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:02] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 19:05:16,759 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:16] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 19:05:17,081 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:17] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-13 19:05:30,231 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:30] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 19:05:30,556 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:30] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 19:05:30,557 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:30] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-13 19:05:30,877 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:30] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:05:31,130 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:31] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:05:35,583 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:35] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:05:35,882 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:35] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 19:05:36,191 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 19:05:36,195 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:36] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 19:05:43,137 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:43] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-13 19:05:43,458 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:43] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-13 19:05:58,350 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:58] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-13 19:05:58,355 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:58] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-13 19:05:58,660 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:05:58] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:06:31,939 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:06:31] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:06:33,271 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:06:33] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:06:33,531 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:06:33] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:08:38,914 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:08:38] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:08:39,167 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:08:39] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:08:42,076 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:08:42] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:08:42,388 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:08:42] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-13 19:08:42,703 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:08:42] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-13 19:08:43,411 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:08:43] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-13 19:08:47,842 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:08:47] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 19:08:47,844 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:08:47] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:08:48,153 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 19:08:48,153 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:08:48] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 19:08:56,642 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:08:56] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:09:02,658 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:09:02] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 19:09:02,662 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 19:09:02,662 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:09:02] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 19:09:02,976 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:09:02] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:09:21,465 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:09:21] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:09:22,327 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:09:22] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:09:22,641 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:09:22] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 19:09:22,952 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 19:09:22,952 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:09:22] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-13 19:10:28,920 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:10:28] "GET /api/students/students HTTP/1.1" 200 -
2025-05-13 19:10:37,670 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:10:37] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-13 19:10:38,050 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:10:38] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 19:10:38,321 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:10:38] "GET /api/users/users HTTP/1.1" 200 -
2025-05-13 19:10:39,162 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:10:39] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-13 19:10:39,477 - werkzeug - INFO - 127.0.0.1 - - [13/May/2025 19:10:39] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 10:17:53,491 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-14 10:17:53,492 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 10:17:53,492 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 10:17:53,491 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-14 10:17:53,492 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 10:17:53,492 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 10:17:53,501 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 10:17:53,501 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 10:18:02,449 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:18:02] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-14 10:18:02,451 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:18:02] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-14 10:18:02,775 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:18:02] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 10:18:03,076 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 10:18:03] "GET /api/users/users HTTP/1.1" 200 -
2025-05-14 13:34:41,941 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-14 13:34:41,942 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:34:42,049 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 13:34:42,050 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:34:42,295 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-14 13:34:42,295 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:34:42,499 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-14 13:34:42,499 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:34:42,710 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-14 13:34:42,710 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:37:29,100 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-14 13:37:29,101 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:37:29,178 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-14 13:37:29,178 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:37:29,400 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-14 13:37:29,400 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:37:29,606 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-14 13:37:29,606 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:37:29,777 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-14 13:37:29,777 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-14 13:37:56,418 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:37:56] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:37:56,680 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:37:56] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:37:56,739 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:37:56] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:38:01,010 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:38:01] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-14 13:38:01,331 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:38:01] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-14 13:38:05,090 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:38:05] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-14 13:38:05,405 - werkzeug - INFO - 127.0.0.1 - - [14/May/2025 13:38:05] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 11:44:30,152 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-15 11:44:30,152 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 11:44:30,274 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-15 11:44:30,274 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 11:44:30,469 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-15 11:44:30,469 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 11:44:30,640 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-15 11:44:30,641 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 11:44:30,808 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-15 11:44:30,808 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 11:45:22,659 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:45:22] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-15 11:45:23,310 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:45:23] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-15 11:45:23,649 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:45:23] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-15 11:45:23,650 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:45:23] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-15 11:45:23,971 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:45:23] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 11:45:24,229 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:45:24] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 11:47:02,788 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:47:02] "OPTIONS /api/users/register HTTP/1.1" 200 -
2025-05-15 11:47:03,272 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:47:03] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 500 -
2025-05-15 11:47:17,842 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:47:17] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-15 11:47:17,845 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:47:17] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 11:47:18,149 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-15 11:47:18,149 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:47:18] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-15 11:47:36,311 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:47:36] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-15 11:47:36,626 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:47:36] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-15 11:48:11,734 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:48:11] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 11:48:12,774 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:48:12] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-15 11:48:12,777 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-15 11:48:12,777 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:48:12] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-15 11:48:13,094 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:48:13] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 11:48:14,767 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:48:14] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 11:48:17,045 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-15 11:48:17,046 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:48:17] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-15 11:48:17,048 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:48:17] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 11:48:18,572 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:48:18] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-15 11:48:18,578 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:48:18] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-15 11:48:19,541 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:48:19] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 11:48:19,858 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:48:19] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-15 11:56:11,290 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-15 11:56:11,290 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 11:56:11,393 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-15 11:56:11,393 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 11:56:11,587 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-15 11:56:11,587 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 11:56:11,778 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-15 11:56:11,778 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 11:56:11,946 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-15 11:56:11,946 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 11:56:30,833 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:56:30] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-15 11:56:31,191 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:56:31] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-15 11:56:31,193 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:56:31] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-15 11:56:31,510 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:56:31] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 11:56:31,771 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:56:31] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 11:56:38,339 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:56:38] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 11:56:38,651 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:56:38] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 11:56:38,961 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:56:38] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 11:56:59,442 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:56:59] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-15 11:56:59,760 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:56:59] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-15 11:57:05,630 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:57:05] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 11:57:06,431 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:57:06] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-15 11:57:06,436 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:57:06] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-15 11:58:48,195 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:58:48] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 11:58:48,461 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:58:48] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 11:58:49,064 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:58:49] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 11:58:49,385 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:58:49] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 11:58:49,697 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:58:49] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 11:58:49,825 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:58:49] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 11:58:49,979 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:58:49] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 11:58:50,011 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:58:50] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 11:58:50,136 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 11:58:50] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 12:46:08,829 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-15 12:46:08,829 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 12:46:09,034 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-15 12:46:09,034 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 12:46:09,137 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-15 12:46:09,137 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 12:46:09,328 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-15 12:46:09,328 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 12:46:09,661 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-15 12:46:09,661 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 12:46:17,273 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:46:17] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-15 12:46:17,275 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:46:17] "OPTIONS /api/users/users HTTP/1.1" 200 -
2025-05-15 12:46:17,596 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:46:17] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 12:46:17,853 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:46:17] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 12:46:21,112 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:46:21] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 12:46:21,431 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:46:21] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 12:46:21,762 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:46:21] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 12:47:03,745 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:47:03] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 12:47:04,055 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:47:04] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 12:47:04,366 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:47:04] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 12:47:09,670 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:47:09] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-15 12:47:10,192 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:47:10] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-15 12:47:10,530 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:47:10] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 12:47:10,781 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:47:10] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 12:47:10,842 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:47:10] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 12:47:32,289 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:47:32] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-15 12:47:32,607 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:47:32] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-15 12:47:33,773 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:47:33] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 12:53:21,404 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:53:21] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-15 12:53:21,738 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:53:21] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 12:53:22,015 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:53:22] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 12:53:22,063 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:53:22] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 12:53:23,754 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:53:23] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-15 12:53:24,070 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:53:24] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-15 12:53:26,661 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:53:26] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 12:53:50,231 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:53:50] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-15 12:53:50,563 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:53:50] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-15 12:53:50,564 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:53:50] "OPTIONS /api/students/students HTTP/1.1" 200 -
2025-05-15 12:53:50,882 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:53:50] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 12:53:51,136 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:53:51] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 12:53:54,129 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:53:54] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-15 12:53:54,132 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-15 12:53:54,133 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:53:54] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-15 12:53:54,134 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:53:54] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 12:53:57,021 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:53:57] "OPTIONS /api/parents/parents HTTP/1.1" 200 -
2025-05-15 12:53:57,334 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:53:57] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-15 12:53:57,592 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:53:57] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-15 12:53:57,886 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:53:57] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 12:53:58,976 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:53:58] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 12:55:07,925 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:55:07] "OPTIONS /api/users/register HTTP/1.1" 200 -
2025-05-15 12:55:08,242 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:55:08] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 500 -
2025-05-15 12:56:39,366 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:56:39] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 12:56:39,628 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:56:39] "GET /api/students/students HTTP/1.1" 200 -
2025-05-15 12:57:26,935 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 12:57:26] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 500 -
2025-05-15 13:06:10,182 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-15 13:06:10,183 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 13:06:10,316 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-15 13:06:10,317 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 13:06:10,530 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-15 13:06:10,530 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 13:06:10,723 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-15 13:06:10,723 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 13:06:10,906 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-15 13:06:10,907 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-15 13:06:37,664 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 13:06:37] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-15 13:06:38,033 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 13:06:38] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 13:06:38,270 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 13:06:38] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 13:06:38,348 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 13:06:38] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 13:06:41,643 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 13:06:41] "OPTIONS /api/courses/courses HTTP/1.1" 200 -
2025-05-15 13:06:41,961 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 13:06:41] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-15 13:06:43,568 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 13:06:43] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 13:07:03,516 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 13:07:03] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 13:07:03,780 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 13:07:03] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-15 13:07:03,838 - werkzeug - INFO - 127.0.0.1 - - [15/May/2025 13:07:03] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 11:26:06,021 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-27 11:26:06,021 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 11:26:06,021 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 11:26:06,021 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 11:26:06,361 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-27 11:26:06,393 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 11:26:06,394 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 11:26:06,599 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-27 11:26:06,599 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 11:26:18,303 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:26:18] "[33mOPTIONS /login HTTP/1.1[0m" 404 -
2025-05-27 11:32:34,122 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-27 11:32:34,122 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 11:32:54,952 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 11:32:54,952 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 11:33:01,391 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-27 11:33:01,392 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 11:33:28,128 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-27 11:33:28,128 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 11:33:35,618 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-27 11:33:35,618 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 11:34:10,606 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:34:10] "OPTIONS /api/auth/login HTTP/1.1" 200 -
2025-05-27 11:35:07,426 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:35:07] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-27 11:35:07,504 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:35:07] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 11:35:07,618 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:35:07] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 11:35:07,792 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:35:07] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 11:35:07,803 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:35:07] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 11:35:07,814 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:35:07] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 11:35:07,959 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:35:07] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 11:35:08,112 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:35:08] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 11:35:08,113 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:35:08] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 11:35:16,063 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:35:16] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 11:35:16,249 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:35:16] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 11:35:16,300 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:35:16] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 11:35:16,300 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:35:16] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 11:35:16,310 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:35:16] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 11:35:16,317 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:35:16] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 11:35:16,573 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:35:16] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 11:35:16,621 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:35:16] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 11:37:06,473 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-27 11:37:06,473 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 11:37:06,584 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 11:37:06,584 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 11:37:06,767 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-27 11:37:06,767 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 11:37:06,952 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-27 11:37:06,952 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 11:37:07,115 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-27 11:37:07,115 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 11:37:22,617 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:22] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 11:37:22,624 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:22] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 11:37:22,625 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:22] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 11:37:22,627 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:22] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 11:37:22,631 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:22] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 11:37:22,633 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:22] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 11:37:22,939 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:22] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 11:37:22,950 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:22] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 11:37:38,460 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:38] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-27 11:37:38,796 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:38] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 11:37:38,797 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:38] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 11:37:38,797 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:38] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 11:37:38,798 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:38] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 11:37:38,806 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:38] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 11:37:39,061 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:39] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 11:37:39,107 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:39] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 11:37:39,107 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:39] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 11:37:45,413 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:45] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-27 11:37:45,746 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:45] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 11:37:45,760 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:45] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 11:37:45,761 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:45] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 11:37:45,759 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-27 11:37:45,762 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:45] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-27 11:37:45,999 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:45] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 11:37:46,067 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-27 11:37:46,068 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:37:46] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-27 11:43:53,351 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:43:53] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-27 11:43:53,407 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:43:53] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 11:43:53,409 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:43:53] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 11:43:53,425 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:43:53] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 11:43:53,716 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:43:53] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 11:43:53,726 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:43:53] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 11:43:53,726 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:43:53] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 11:43:53,749 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:43:53] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 11:43:54,025 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:43:54] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 11:43:57,330 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:43:57] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 11:43:57,343 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:43:57] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 11:43:57,500 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:43:57] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 11:43:57,578 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:43:57] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 11:43:57,578 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:43:57] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 11:43:57,603 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:43:57] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 11:43:57,669 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:43:57] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 11:43:57,819 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:43:57] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 11:45:30,092 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:45:30] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 11:45:30,108 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:45:30] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 11:45:30,154 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:45:30] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 11:45:30,280 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:45:30] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 11:45:30,280 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:45:30] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 11:45:30,299 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:45:30] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 11:45:30,415 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:45:30] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 11:45:30,463 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:45:30] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 11:45:33,975 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:45:33] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 11:45:34,291 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:45:34] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 11:45:36,380 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:45:36] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 11:45:38,365 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:45:38] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 11:45:40,083 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:45:40] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 11:45:40,388 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:45:40] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 11:45:41,639 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:45:41] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 11:45:41,946 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:45:41] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 11:45:44,048 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 11:45:44] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:01:03,268 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:01:03] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:01:03,369 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:01:03] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:03:09,816 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:03:09] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:03:09,838 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:03:09] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:04:33,258 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:04:33] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:04:33,476 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:04:33] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 12:04:33,476 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:04:33] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 12:04:33,477 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:04:33] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:04:34,383 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:04:34] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:04:35,753 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:04:35] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 12:04:36,065 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:04:36] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:04:37,275 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:04:37] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:04:38,329 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:04:38] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 12:04:38,330 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:04:38] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:04:49,804 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:04:49] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 12:04:49,806 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:04:49] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:04:49,906 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:04:49] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 12:04:50,001 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:04:50] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:11:04,330 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:04] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 12:11:04,330 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:04] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:11:04,331 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:04] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 12:11:04,341 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:04] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:11:04,342 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:04] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 12:11:04,343 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:04] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:11:04,650 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:04] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 12:11:04,655 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:04] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:11:16,102 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:16] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:11:18,530 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:18] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 12:11:18,530 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:18] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:11:21,464 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:21] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:11:24,469 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:24] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 12:11:24,470 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:24] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:11:28,250 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:28] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:11:33,539 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:33] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-27 12:11:33,590 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:33] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:11:33,885 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:33] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 12:11:33,886 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:33] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:11:33,886 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:33] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 12:11:33,923 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:33] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:11:34,211 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:34] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 12:11:34,212 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:34] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:11:34,212 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:34] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 12:11:41,834 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:41] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-27 12:11:41,907 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:41] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:11:42,198 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:42] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:11:42,198 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:42] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 12:11:42,247 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:42] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:11:42,510 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:42] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:11:42,510 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:42] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 12:11:42,510 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:11:42] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 12:16:03,128 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:03] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 12:16:03,129 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:03] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:16:03,145 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:03] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:16:03,215 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:03] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 12:16:03,279 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:03] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 12:16:03,280 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:03] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:16:03,453 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:03] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 12:16:03,464 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:03] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:16:30,233 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:30] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 12:16:30,235 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:30] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:16:30,242 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:30] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:16:30,293 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:30] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 12:16:30,302 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:30] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 12:16:30,302 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:30] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:16:30,560 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:30] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:16:30,574 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:30] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 12:16:33,208 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:33] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 12:16:33,208 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:33] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:16:33,215 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:33] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:16:33,223 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:33] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 12:16:33,273 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:33] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 12:16:33,280 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:33] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:16:33,523 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:33] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 12:16:33,529 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:33] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:16:36,849 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:36] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 12:16:36,860 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:36] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:16:37,049 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:37] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:16:37,050 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:37] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 12:16:37,329 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:37] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 12:16:37,339 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:37] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:16:37,367 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:37] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 12:16:37,367 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:37] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:16:37,374 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:37] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 12:16:37,381 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:37] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:16:37,651 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:37] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 12:16:37,655 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:16:37] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:17:24,330 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:17:24] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 12:17:24,332 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:17:24] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 12:17:24,332 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:17:24] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:17:24,339 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:17:24] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 12:17:24,340 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:17:24] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:17:24,382 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:17:24] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 12:17:24,645 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:17:24] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:17:24,652 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:17:24] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:19:07,333 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:19:07] "[33mOPTIONS /courses HTTP/1.1[0m" 404 -
2025-05-27 12:19:07,333 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:19:07] "[33mOPTIONS /parents HTTP/1.1[0m" 404 -
2025-05-27 12:19:07,333 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:19:07] "[33mOPTIONS /users HTTP/1.1[0m" 404 -
2025-05-27 12:19:07,334 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:19:07] "[33mOPTIONS /students HTTP/1.1[0m" 404 -
2025-05-27 12:19:07,334 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:19:07] "[33mOPTIONS /courses HTTP/1.1[0m" 404 -
2025-05-27 12:19:07,335 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:19:07] "[33mOPTIONS /users HTTP/1.1[0m" 404 -
2025-05-27 12:19:07,335 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:19:07] "[33mOPTIONS /parents HTTP/1.1[0m" 404 -
2025-05-27 12:19:07,335 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:19:07] "[33mOPTIONS /students HTTP/1.1[0m" 404 -
2025-05-27 12:19:13,096 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:19:13] "[33mOPTIONS /users HTTP/1.1[0m" 404 -
2025-05-27 12:19:13,096 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:19:13] "[33mOPTIONS /students HTTP/1.1[0m" 404 -
2025-05-27 12:19:13,096 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:19:13] "[33mOPTIONS /courses HTTP/1.1[0m" 404 -
2025-05-27 12:19:13,097 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:19:13] "[33mOPTIONS /parents HTTP/1.1[0m" 404 -
2025-05-27 12:19:13,409 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:19:13] "[33mOPTIONS /courses HTTP/1.1[0m" 404 -
2025-05-27 12:19:13,409 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:19:13] "[33mOPTIONS /users HTTP/1.1[0m" 404 -
2025-05-27 12:19:13,410 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:19:13] "[33mOPTIONS /parents HTTP/1.1[0m" 404 -
2025-05-27 12:19:13,411 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:19:13] "[33mOPTIONS /students HTTP/1.1[0m" 404 -
2025-05-27 12:19:46,709 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:19:46] "GET /api/courses/courses HTTP/1.1" 200 -

2025-05-27 12:19:46,718 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:19:46] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:19:46,721 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:19:46] "GET /api/parents/parents HTTP/1.1" 200 -

2025-05-27 12:19:47,027 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:19:47] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 12:19:47,031 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:19:47] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:58:54,065 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:58:54] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 12:58:54,066 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:58:54] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:58:54,079 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:58:54] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:58:54,192 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:58:54] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 12:58:54,193 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:58:54] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 12:58:54,193 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:58:54] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 12:58:54,380 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:58:54] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 12:58:54,390 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 12:58:54] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 13:03:58,784 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:03:58] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 13:03:58,785 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:03:58] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 13:03:58,796 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:03:58] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:03:58,934 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:03:58] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 13:03:58,934 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:03:58] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 13:03:58,935 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:03:58] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 13:03:59,108 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:03:59] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 13:03:59,110 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:03:59] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:03:59,476 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:03:59] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 13:03:59,486 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:03:59] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:03:59,580 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:03:59] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 13:03:59,629 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:03:59] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 13:03:59,660 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:03:59] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 13:03:59,666 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:03:59] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:03:59,801 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:03:59] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 13:03:59,803 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:03:59] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 13:06:31,954 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:06:31] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 13:06:31,967 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:06:31] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:06:32,010 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:06:32] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 13:06:32,165 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:06:32] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 13:06:32,169 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:06:32] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 13:06:32,186 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:06:32] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:06:32,288 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:06:32] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 13:06:32,321 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:06:32] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 13:06:37,166 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:06:37] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:06:40,357 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:06:40] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 13:06:40,380 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:06:40] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 13:06:41,248 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:06:41] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 13:06:42,540 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:06:42] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 13:06:42,548 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:06:42] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 13:06:45,629 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:06:45] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:07:16,922 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:07:16] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 13:07:16,922 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:07:16] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 13:07:16,922 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:07:16] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 13:07:16,933 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:07:16] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:07:24,853 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:07:24] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:08:17,938 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:08:17] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 13:08:17,968 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:08:17] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 13:08:20,187 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:08:20] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 13:08:22,589 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:08:22] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 13:08:22,899 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:08:22] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 13:08:24,732 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:08:24] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:08:34,497 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:08:34] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 13:08:34,505 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:08:34] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:08:34,806 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:08:34] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 13:08:34,806 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:08:34] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 13:08:39,167 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:08:39] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:08:41,810 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:08:41] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 13:08:41,822 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:08:41] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:08:42,125 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:08:42] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 13:08:42,127 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:08:42] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 13:14:26,675 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:14:26] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:14:31,676 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:14:31] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 13:14:31,677 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:14:31] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 13:14:33,928 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:14:33] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:16:58,647 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:16:58] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 13:16:58,657 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:16:58] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 13:16:59,718 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:16:59] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:17:04,201 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:17:04] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:17:04,491 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:17:04] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 13:17:04,491 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:17:04] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 13:17:04,494 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:17:04] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 13:20:45,092 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 13:20:45,093 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 13:20:45,096 - werkzeug - INFO -  * Restarting with stat
2025-05-27 13:20:46,104 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 13:20:46,137 - werkzeug - INFO -  * Debugger PIN: 330-************-05-27 13:22:15,149 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 13:22:15,149 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 13:22:15,151 - werkzeug - INFO -  * Restarting with stat
2025-05-27 13:22:16,038 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 13:22:16,040 - werkzeug - INFO -  * Debugger PIN: 330-************-05-27 13:23:57,305 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 13:23:57,305 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 13:27:13,512 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-27 13:27:34,089 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 13:27:34,089 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 13:27:34,092 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-27 13:27:34,093 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 13:27:34,322 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-27 13:27:34,322 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 13:27:34,571 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-27 13:27:34,571 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 13:27:34,724 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-27 13:27:34,724 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 13:27:46,349 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:27:46] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 13:27:46,467 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:27:46] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:27:46,512 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:27:46] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 13:27:46,582 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:27:46] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 13:27:46,590 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:27:46] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 13:27:46,604 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:27:46] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:27:46,787 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:27:46] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 13:27:46,825 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:27:46] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 13:27:48,990 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:27:48] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:27:51,981 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:27:51] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 13:27:51,981 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:27:51] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 13:27:52,945 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:27:52] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 13:27:54,245 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:27:54] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:28:07,725 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:28:07] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:28:07,760 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:28:07] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:29:38,002 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:29:38] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 13:29:38,038 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 13:29:38] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 14:43:33,496 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-27 14:43:33,496 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 14:43:33,563 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 14:43:33,563 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 14:43:33,720 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-27 14:43:33,720 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 14:43:33,917 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-27 14:43:33,917 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 14:43:34,146 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-27 14:43:34,146 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 14:43:45,724 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 14:43:45] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 14:43:45,731 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 14:43:45] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 14:43:45,732 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 14:43:45] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 14:43:45,735 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 14:43:45] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 14:43:45,741 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 14:43:45] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 14:43:45,741 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 14:43:45] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 14:43:46,044 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 14:43:46] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 14:43:46,057 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 14:43:46] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 14:43:52,198 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 14:43:52] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 14:44:03,699 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-27 14:44:03,699 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 14:44:03] "[31m[1mGET /api/users/users HTTP/1.1[0m" 401 -
2025-05-27 14:48:39,548 - common.middleware - WARNING - Expired token for path: /api/users/users
2025-05-27 14:48:39,548 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 14:48:39] "[31m[1mGET /api/users/users HTTP/1.1[0m" 401 -
2025-05-27 14:49:04,858 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 14:49:04] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-27 14:49:29,185 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 14:49:29] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 14:50:03,078 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 14:50:03,078 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 14:50:29,572 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 14:50:29,572 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 14:50:29,575 - werkzeug - INFO -  * Restarting with stat
2025-05-27 14:50:30,439 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 14:50:30,442 - werkzeug - INFO -  * Debugger PIN: 330-************-05-27 14:50:35,730 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 14:50:35] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 14:53:34,376 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-27 14:53:34,376 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 14:53:34,565 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 14:53:34,565 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 14:53:34,759 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-27 14:53:34,759 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 14:53:34,968 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-27 14:53:34,968 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 14:53:35,172 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-27 14:53:35,172 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 14:53:37,483 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 14:53:37] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 14:54:48,034 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 14:54:48] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 15:00:39,307 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:00:39] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 15:00:48,372 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 15:00:48,372 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:01:30,504 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:01:30] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 15:01:32,568 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:01:32] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 500 -
2025-05-27 15:01:42,475 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-27 15:01:42,475 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:01:42,675 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 15:01:42,675 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:01:42,984 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-27 15:01:42,984 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:01:43,094 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-27 15:01:43,094 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:01:43,308 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-27 15:01:43,308 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:01:47,005 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:01:47] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 15:03:22,118 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 15:03:22,118 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:03:25,391 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:03:25] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 15:04:01,688 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:04:01] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 15:04:03,735 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:04:03] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 500 -
2025-05-27 15:05:10,328 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 15:05:10,328 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:05:32,796 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:05:32] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 15:05:34,854 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:05:34] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 500 -
2025-05-27 15:06:46,942 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 15:06:46,942 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:07:08,908 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:07:08] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 15:07:10,968 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:07:10] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 500 -
2025-05-27 15:09:05,657 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 15:09:05,657 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:09:59,052 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:09:59] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 15:11:26,981 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:11:26] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 15:11:28,134 - common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-27 15:11:28,134 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:11:28] "[31m[1mGET /api/users/users HTTP/1.1[0m" 401 -
2025-05-27 15:12:11,613 - common.middleware - WARNING - Invalid token for path: /api/users/users
2025-05-27 15:12:11,613 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:12:11] "[31m[1mGET /api/users/users HTTP/1.1[0m" 401 -
2025-05-27 15:12:56,915 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:12:56] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 15:14:56,371 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 15:14:56,372 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:15:41,822 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:15:41] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:16:18,546 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:16:18] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-27 15:16:48,622 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:16:48] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:17:15,213 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:17:15] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:17:15,224 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:17:15] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:17:20,765 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:17:20] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 15:17:20,766 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:17:20] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:17:23,322 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:17:23] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:17:26,407 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:17:26] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:17:26,414 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:17:26] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:17:31,326 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:17:31] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:17:37,187 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:17:37] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-27 15:17:37,217 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:17:37] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:17:39,832 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:17:39] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:17:39,830 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-27 15:17:39,833 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:17:39] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-27 15:17:40,425 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:17:40] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:17:40,593 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-27 15:17:40,594 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:17:40] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-27 15:17:40,646 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:17:40] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:17:40,646 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:17:40] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:17:40,652 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-27 15:17:40,653 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:17:40] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-27 15:17:40,657 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:17:40] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:17:44,510 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:17:44] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:17:48,070 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-27 15:17:48,071 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:17:48] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-27 15:17:48,073 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:17:48] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:17:51,164 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:17:51] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:17:51,166 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:17:51] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:19:24,597 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:19:24] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-27 15:19:24,641 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:19:24] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:19:24,941 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:19:24] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 15:19:24,942 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:19:24] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:19:24,947 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:19:24] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:19:24,949 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:19:24] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:19:25,252 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:19:25] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:19:25,253 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:19:25] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 15:19:25,259 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:19:25] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:19:27,136 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:19:27] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:20:07,734 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:20:07] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-27 15:20:08,074 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:20:08] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 201 -
2025-05-27 15:20:08,313 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:20:08] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:22:34,495 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:22:34] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:22:34,504 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:22:34] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:22:40,243 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:22:40] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-27 15:22:40,283 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:22:40] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 15:22:40,283 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:22:40] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:22:40,283 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:22:40] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:22:40,303 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:22:40] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 15:22:40,591 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:22:40] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 15:22:40,591 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:22:40] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:22:40,595 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:22:40] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:22:40,636 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:22:40] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 15:22:43,631 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:22:43] "[35m[1mGET /api/users/users HTTP/1.1[0m" 500 -
2025-05-27 15:30:18,242 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-27 15:30:18,242 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:30:18,394 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 15:30:18,394 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:30:18,653 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-27 15:30:18,653 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:30:18,867 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-27 15:30:18,867 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:30:19,089 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-27 15:30:19,089 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:30:33,907 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:30:33] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-27 15:30:33,969 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:30:33] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:30:34,243 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:30:34] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:30:34,247 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:30:34] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:30:34,253 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:30:34] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 15:30:34,255 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:30:34] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:30:34,256 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:30:34] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:30:34,562 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:30:34] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 15:30:34,564 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:30:34] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:30:36,296 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:30:36] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:33:11,754 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:33:11] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-27 15:33:11,789 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:33:11] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:33:12,088 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:33:12] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 15:33:12,089 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:33:12] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:33:12,089 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:33:12] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:33:12,098 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:33:12] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:33:12,403 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:33:12] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 15:33:12,403 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:33:12] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:33:12,404 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:33:12] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:33:17,981 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:33:17] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:33:50,196 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:33:50] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-27 15:33:50,518 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:33:50] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 201 -
2025-05-27 15:33:50,774 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:33:50] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:39:49,140 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-27 15:39:49,140 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:39:49,216 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 15:39:49,217 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:39:49,443 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-27 15:39:49,443 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:39:49,663 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-27 15:39:49,663 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:39:49,848 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-27 15:39:49,849 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:40:00,004 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:40:00] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:40:00,012 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:40:00] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:40:04,783 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:40:04] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:40:41,146 - user_service.common.utils - ERROR - Error: Email already exists, Status: 400
2025-05-27 15:40:41,148 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:40:41] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 400 -
2025-05-27 15:40:49,517 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:40:49] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-27 15:40:49,529 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:40:49] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:41:08,491 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:41:08] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 15:41:08,530 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:41:08] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:41:32,491 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:41:32] "[35m[1mPOST /api/courses/courses HTTP/1.1[0m" 201 -
2025-05-27 15:41:32,499 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:41:32] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 15:42:03,862 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:42:03] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:42:40,222 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:42:40] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-27 15:42:40,248 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:42:40] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 201 -
2025-05-27 15:42:40,573 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:42:40] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:42:43,961 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:42:43] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:42:44,269 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:42:44] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:43:26,443 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:43:26] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-27 15:43:26,479 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:43:26] "[35m[1mPOST /api/parents/parents HTTP/1.1[0m" 201 -
2025-05-27 15:43:26,794 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:43:26] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:43:36,842 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:43:36] "[31m[1mGET /api/students/student-parents/16 HTTP/1.1[0m" 405 -
2025-05-27 15:43:48,454 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:43:48] "[31m[1mGET /api/parents/8 HTTP/1.1[0m" 405 -
2025-05-27 15:43:48,456 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 15:43:48,457 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:43:48] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-27 15:43:59,751 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:43:59] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:44:05,825 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:44:05] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 15:44:05,834 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:44:05] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:44:16,909 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:44:16] "[35m[1mPOST /api/courses/map-student HTTP/1.1[0m" 201 -
2025-05-27 15:44:23,799 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:44:23] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:44:24,106 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:44:24] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:44:32,059 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:44:32] "[31m[1mGET /api/parents/8 HTTP/1.1[0m" 405 -
2025-05-27 15:44:32,061 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 15:44:32,062 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:44:32] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-27 15:44:55,063 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:44:55] "[31m[1mGET /api/parents/8 HTTP/1.1[0m" 405 -
2025-05-27 15:44:55,064 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 15:44:55,065 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:44:55] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-27 15:49:09,762 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:49:09] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:49:09,773 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:49:09] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:49:14,508 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:49:14] "[31m[1mGET /api/students/student-parents/16 HTTP/1.1[0m" 405 -
2025-05-27 15:49:21,994 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 15:49:21,995 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:49:21] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-27 15:50:08,748 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:50:08] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-27 15:50:18,281 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:50:18] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:50:18,387 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:50:18] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:50:54,330 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:50:54] "[31m[1mGET /api/students/student-parents/16 HTTP/1.1[0m" 405 -
2025-05-27 15:51:02,108 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 15:51:02,110 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:51:02] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-27 15:51:31,535 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:51:31] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:51:31,546 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:51:31] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:51:36,615 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:51:36] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:51:36,623 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:51:36] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:52:36,360 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-27 15:52:36,361 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:52:36,622 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 15:52:36,622 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:52:36,726 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-27 15:52:36,726 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:52:36,955 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-27 15:52:36,955 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:52:37,131 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-27 15:52:37,132 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 15:52:44,833 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:52:44] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:52:44,839 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:52:44] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:52:44,843 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:52:44] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:52:44,849 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:52:44] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:52:48,309 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:52:48] "[31m[1mGET /api/students/student-parents/16 HTTP/1.1[0m" 405 -
2025-05-27 15:52:56,894 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:52:56] "[31m[1mGET /api/parents/8 HTTP/1.1[0m" 405 -
2025-05-27 15:52:56,896 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 15:52:56,897 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:52:56] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-27 15:53:05,001 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:53:05] "[31m[1mGET /api/parents/8 HTTP/1.1[0m" 405 -
2025-05-27 15:53:05,002 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 15:53:05,003 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:53:05] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-27 15:54:53,517 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/students
2025-05-27 15:54:53,518 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:54:53] "[31m[1mGET /api/students/students HTTP/1.1[0m" 401 -
2025-05-27 15:55:10,882 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:55:10] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:55:10,884 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:55:10] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:55:10,896 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:55:10] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:55:10,896 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:55:10] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:56:37,974 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:37] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:56:37,974 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:37] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:56:37,987 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:37] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:56:37,988 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:37] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:56:37,988 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:37] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:56:37,988 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:37] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 15:56:38,310 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:38] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 15:56:38,311 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:38] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:56:57,445 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:57] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 15:56:57,445 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:57] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:56:57,617 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:57] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:56:57,694 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:57] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:56:57,697 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:57] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 15:56:57,704 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:57] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:56:57,772 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:57] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:56:57,939 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:57] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:56:58,123 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:58] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:56:58,341 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:58] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 15:56:58,378 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:58] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:56:58,380 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:58] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:56:58,382 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:58] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:56:58,390 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:58] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 15:56:58,662 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:58] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:56:58,695 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:56:58] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:57:49,621 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:57:49] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 15:57:49,621 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:57:49] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:57:49,621 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:57:49] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:57:49,632 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:57:49] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:57:49,632 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:57:49] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 15:57:49,672 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:57:49] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:57:49,932 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:57:49] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:57:49,980 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:57:49] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:57:55,428 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:57:55] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:57:55,684 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:57:55] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:58:01,166 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:58:01] "[31m[1mGET /api/students/student-parents/16 HTTP/1.1[0m" 405 -
2025-05-27 15:58:22,428 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:58:22] "[31m[1mGET /api/parents/8 HTTP/1.1[0m" 405 -
2025-05-27 15:58:22,429 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 15:58:22,430 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:58:22] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-27 15:58:58,891 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:58:58] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:58:58,891 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:58:58] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:58:58,898 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:58:58] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:58:58,899 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:58:58] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:59:12,868 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:59:12] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:59:12,869 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:59:12] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 15:59:12,870 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:59:12] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:59:12,871 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:59:12] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:59:12,887 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:59:12] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:59:12,893 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:59:12] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 15:59:13,182 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:59:13] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:59:13,182 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:59:13] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 15:59:21,667 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:59:21] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 15:59:21,907 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:59:21] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 15:59:29,718 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:59:29] "[31m[1mGET /api/students/student-parents/15 HTTP/1.1[0m" 405 -
2025-05-27 15:59:37,928 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:59:37] "[31m[1mGET /api/parents/9 HTTP/1.1[0m" 405 -
2025-05-27 15:59:37,929 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 15:59:37,930 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 15:59:37] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-27 16:05:46,570 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:05:46] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:05:46,571 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:05:46] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:05:46,594 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:05:46] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:05:46,734 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:05:46] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:08:40,006 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:08:40] "[31m[1mGET /api/students/student-parents/16 HTTP/1.1[0m" 405 -
2025-05-27 16:08:54,388 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:08:54] "[31m[1mGET /api/parents/9 HTTP/1.1[0m" 405 -
2025-05-27 16:08:54,389 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 16:08:54,390 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:08:54] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-27 16:09:04,174 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:09:04] "[31m[1mGET /api/parents/9 HTTP/1.1[0m" 405 -
2025-05-27 16:09:04,175 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 16:09:04,176 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:09:04] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-27 16:09:09,948 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:09:09] "[31m[1mGET /api/parents/9 HTTP/1.1[0m" 405 -
2025-05-27 16:09:09,948 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 16:09:09,950 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:09:09] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-27 16:11:32,416 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:11:32] "[31m[1mGET /api/students/student-parents/15 HTTP/1.1[0m" 405 -
2025-05-27 16:11:37,877 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:11:37] "[31m[1mGET /api/parents/9 HTTP/1.1[0m" 405 -
2025-05-27 16:11:37,878 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 16:11:37,879 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:11:37] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-27 16:15:11,631 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:11] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:15:11,634 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:11] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:15:11,645 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:11] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:15:11,807 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:11] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:15:16,294 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:16] "[31m[1mGET /api/students/student-parents/16 HTTP/1.1[0m" 405 -
2025-05-27 16:15:33,463 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:33] "[31m[1mGET /api/parents/9 HTTP/1.1[0m" 405 -
2025-05-27 16:15:33,464 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 16:15:33,466 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:33] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-27 16:18:23,655 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-27 16:18:23,655 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 16:18:48,517 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-27 16:18:48,517 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 16:19:04,642 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-27 16:19:04,642 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 16:19:41,003 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/student-parents/15
2025-05-27 16:19:41,004 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:19:41] "[31m[1mGET /api/students/student-parents/15 HTTP/1.1[0m" 401 -
2025-05-27 16:19:43,049 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/map-parent
2025-05-27 16:19:43,050 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:19:43] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-27 16:22:20,990 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:22:20] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:22:20,997 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:22:20] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:22:20,998 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:22:20] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:22:21,161 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:22:21] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:22:24,346 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:22:24] "[31m[1mGET /api/students/student-parents/16 HTTP/1.1[0m" 405 -
2025-05-27 16:22:31,690 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:22:31] "[31m[1mGET /api/parents/9 HTTP/1.1[0m" 405 -
2025-05-27 16:22:31,691 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 16:22:31,692 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:22:31] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-27 16:26:15,476 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:26:15] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:26:15,478 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:26:15] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:26:15,486 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:26:15] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:26:15,611 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:26:15] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:26:19,626 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:26:19] "[31m[1mGET /api/students/student-parents/16 HTTP/1.1[0m" 405 -
2025-05-27 16:26:47,325 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:26:47] "[31m[1mGET /api/parents/8 HTTP/1.1[0m" 405 -
2025-05-27 16:26:47,326 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-27 16:26:47,327 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:26:47] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-27 16:32:15,218 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-27 16:32:15,218 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 16:33:07,105 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-27 16:33:07,105 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 16:34:13,180 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:34:13] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:34:13,181 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:34:13] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:34:13,201 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:34:13] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:34:13,338 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:34:13] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:34:21,013 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:34:21] "[31m[1mGET /api/students/student-parents/16 HTTP/1.1[0m" 405 -
2025-05-27 16:35:00,520 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:35:00] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:35:00,567 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:35:00] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:35:45,983 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:35:45] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:35:46,027 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:35:46] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:35:59,984 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:35:59] "GET /api/parents/parents/7 HTTP/1.1" 200 -
2025-05-27 16:36:27,620 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:36:27] "GET /api/parents/parents/9 HTTP/1.1" 200 -
2025-05-27 16:37:04,359 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:37:04] "GET /api/parents/parents/9 HTTP/1.1" 200 -
2025-05-27 16:40:44,259 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:40:44] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-27 16:41:06,211 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:41:06] "GET /api/parents/parents/9 HTTP/1.1" 200 -
2025-05-27 16:43:15,344 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-27 16:43:15,344 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 16:43:20,124 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:43:20] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:43:20,151 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:43:20] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:43:20,223 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:43:20] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:43:20,224 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:43:20] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:43:28,353 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:43:28] "GET /api/parents/parents/9 HTTP/1.1" 200 -
2025-05-27 16:43:28,355 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:43:28] "GET /api/students/student-parents/15 HTTP/1.1" 200 -
2025-05-27 16:44:05,146 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:44:05] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:44:05,148 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:44:05] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:44:05,185 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:44:05] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:44:05,309 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:44:05] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:44:10,337 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:44:10] "GET /api/parents/parents/7 HTTP/1.1" 200 -
2025-05-27 16:44:10,339 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:44:10] "GET /api/students/student-parents/16 HTTP/1.1" 200 -
2025-05-27 16:44:12,503 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:44:12] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:44:12,507 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:44:12] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:44:12,512 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:44:12] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:44:12,703 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:44:12] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:44:15,898 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:44:15] "GET /api/students/student-parents/15 HTTP/1.1" 200 -
2025-05-27 16:44:23,007 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:44:23] "GET /api/parents/parents/9 HTTP/1.1" 200 -
2025-05-27 16:44:23,021 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:44:23] "[35m[1mPOST /api/students/map-parent HTTP/1.1[0m" 201 -
2025-05-27 16:44:46,521 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:44:46] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 16:44:46,575 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:44:46] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 16:44:46,791 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:44:46] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:44:46,791 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:44:46] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:44:50,434 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:44:50] "GET /api/users/users HTTP/1.1" 200 -
2025-05-27 16:44:55,356 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:44:55] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 16:44:55,357 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:44:55] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:44:59,471 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:44:59] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:45:04,242 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:45:04] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:45:04,242 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:45:04] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:47:44,272 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:47:44] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-27 16:47:44,308 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:47:44] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:47:44,614 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-27 16:47:44,616 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:47:44] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-27 16:47:44,623 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:47:44] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:47:44,624 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:47:44] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:47:44,936 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-27 16:47:44,936 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:47:44] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-27 16:47:44,938 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:47:44] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:47:47,500 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-27 16:47:47,500 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:47:47] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-27 16:47:47,502 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:47:47] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:47:47,605 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:47:47] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:47:47,695 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-27 16:47:47,695 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:47:47] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 403 -
2025-05-27 16:47:47,704 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:47:47] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:47:47,708 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:47:47] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 16:47:50,383 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:47:50] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:50:30,532 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:50:30] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:50:30,539 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:50:30] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:55:12,765 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:55:12] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 16:55:12,774 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:55:12] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:06:38,873 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:06:38] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:06:38,880 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:06:38] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:06:39,859 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:06:39] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:06:39,865 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:06:39] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:06:41,865 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:06:41] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:06:41,872 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:06:41] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:06:45,880 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:06:45] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:06:45,886 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:06:45] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:06:47,850 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:06:47] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:06:47,857 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:06:47] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:06:58,856 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:06:58] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:06:58,862 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:06:58] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:07:06,858 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:07:06] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:07:06,864 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:07:06] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:07:11,846 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:07:11] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:07:11,852 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:07:11] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:07:15,846 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:07:15] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:07:15,853 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:07:15] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:07:43,863 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:07:43] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:07:43,869 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:07:43] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:07:47,868 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:07:47] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:07:47,874 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:07:47] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:07:53,868 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:07:53] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:07:53,875 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:07:53] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:08:03,874 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:08:03] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:08:03,880 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:08:03] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:08:09,884 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:08:09] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:08:09,890 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:08:09] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:08:13,886 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:08:13] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:08:13,893 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:08:13] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:08:20,860 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:08:20] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:08:20,867 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:08:20] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:08:21,873 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:08:21] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:08:21,879 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:08:21] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:08:32,876 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:08:32] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:08:32,883 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:08:32] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:08:35,862 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:08:35] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:08:35,869 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:08:35] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:08:44,872 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:08:44] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:08:44,878 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:08:44] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:08:50,884 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:08:50] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:08:50,892 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:08:50] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:08:55,886 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:08:55] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:08:55,892 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:08:55] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:09:01,857 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:09:01] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:09:01,863 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:09:01] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:09:02,857 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:09:02] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:09:02,866 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:09:02] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:09:04,854 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:09:04] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:09:04,861 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:09:04] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:09:09,877 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:09:09] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:09:09,883 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:09:09] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:09:13,857 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:09:13] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:09:13,867 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:09:13] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:09:31,861 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:09:31] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:09:31,868 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:09:31] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:09:39,859 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:09:39] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:09:39,866 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:09:39] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:09:46,871 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:09:46] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:09:46,878 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:09:46] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:09:47,854 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:09:47] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 17:09:47,862 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 17:09:47] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:01:40,134 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-27 18:01:40,134 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:02:17,680 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-27 18:02:17,690 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:02:17] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-27 18:03:02,796 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-27 18:03:02,799 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:03:02] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-27 18:03:05,071 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-27 18:03:05,072 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:03:05] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-27 18:03:07,316 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-27 18:03:07,318 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:03:07] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-27 18:03:09,564 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-27 18:03:09,565 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:03:09] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-27 18:03:11,819 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-27 18:03:11,820 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:03:11] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-27 18:03:21,984 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 18:03:21,984 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:04:07,077 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-27 18:04:07,078 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:04:07] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-27 18:04:09,132 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-27 18:04:09,133 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:04:09] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-27 18:04:11,446 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-27 18:04:11,446 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:04:11] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-27 18:04:45,152 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:04:45] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:04:45,161 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:04:45] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:05:47,616 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-27 18:05:47,617 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:05:47] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-27 18:05:49,869 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-27 18:05:49,871 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:05:49] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-27 18:05:52,129 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-27 18:05:52,130 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:05:52] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-27 18:05:54,385 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-27 18:05:54,386 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:05:54] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-27 18:05:56,637 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-27 18:05:56,638 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:05:56] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-27 18:05:58,891 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-27 18:05:58,892 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:05:58] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-27 18:06:14,173 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-27 18:06:14,174 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:07:16,399 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:07:16] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-27 18:08:14,168 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-27 18:08:14,169 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:08:14] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-27 18:09:24,085 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-27 18:09:24,085 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:09:24,324 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 18:09:24,324 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:09:24,538 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-27 18:09:24,538 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:09:24,740 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-27 18:09:24,741 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:09:24,940 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-27 18:09:24,940 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:09:32,788 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:09:32] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:09:32,796 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:09:32] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:16:49,029 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-27 18:16:49,029 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:16:49,152 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 18:16:49,152 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:16:49,383 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-27 18:16:49,383 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:16:49,583 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-27 18:16:49,583 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:16:49,788 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-27 18:16:49,788 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:29:46,853 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:29:46] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 18:29:46,861 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:29:46] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:29:46,862 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:29:46] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 18:29:46,988 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:29:46] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 18:29:46,999 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:29:46] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 18:29:46,999 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:29:46] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:29:57,669 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:29:57] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:31:09,865 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:31:09] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 18:31:09,911 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:31:09] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:31:48,907 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:31:48] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 18:31:48,907 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:31:48] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:31:48,926 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:31:48] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 18:31:49,086 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:31:49] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:32:33,801 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:32:33] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 18:32:33,874 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:32:33] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:32:33,952 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:32:33] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 18:32:33,984 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:32:33] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:35:27,960 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-27 18:35:27,960 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
elopment server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 18:35:27,960 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:35:28,064 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-27 18:35:28,064 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:35:28,263 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-27 18:35:28,264 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:35:28,482 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-27 18:35:28,482 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:35:38,222 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:35:38] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:35:38,259 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:35:38] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:35:38,383 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:35:38] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 18:35:52,896 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:35:52] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-27 18:37:33,397 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:37:33] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:38:08,981 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:38:08] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 201 -
2025-05-27 18:38:33,456 - student_service.common.utils - ERROR - Error: Student with this user_id already exists, Status: 400
2025-05-27 18:38:33,458 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:38:33] "[31m[1mPOST /api/students/students HTTP/1.1[0m" 400 -
2025-05-27 18:38:49,222 - student_service.common.utils - ERROR - Error: Missing required fields: user_id, Status: 400
2025-05-27 18:38:49,223 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:38:49] "[31m[1mPOST /api/students/students HTTP/1.1[0m" 400 -
2025-05-27 18:39:02,791 - student_service.common.utils - ERROR - Error: Student with this user_id already exists, Status: 400
2025-05-27 18:39:02,792 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:39:02] "[31m[1mPOST /api/students/students HTTP/1.1[0m" 400 -
2025-05-27 18:39:09,954 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:39:09] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 201 -
2025-05-27 18:42:49,858 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:42:49] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:45:54,189 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:45:54] "GET /api/courses/courses HTTP/1.1" 200 -

2025-05-27 18:45:54,199 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:45:54] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 18:45:54,203 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:45:54] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:45:54,204 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:45:54] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 18:45:54,209 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:45:54] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 18:46:23,175 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:46:23] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 18:46:23,175 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:46:23] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:46:23,184 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:46:23] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 18:46:23,185 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:46:23] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 18:46:23,185 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:46:23] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:46:25,247 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:46:25] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:48:04,034 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-27 18:48:04,034 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:48:33,570 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-27 18:48:33,572 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:48:33] "[31m[1mGET /api/users/users HTTP/1.1[0m" 401 -
2025-05-27 18:50:53,604 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:50:53] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:50:53,614 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:50:53] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:52:34,369 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:52:34] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:52:34,452 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:52:34] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:54:55,849 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:54:55] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:57:41,193 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:57:41] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-27 18:57:41,275 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:57:41] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 18:57:41,280 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:57:41] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:57:41,551 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:57:41] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 18:57:41,552 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:57:41] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 18:57:41,595 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:57:41] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 18:57:41,595 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:57:41] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:57:57,160 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:57:57] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:59:47,124 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:47] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 18:59:47,124 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:47] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 18:59:47,125 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:47] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:59:47,139 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:47] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-27 18:59:47,140 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:47] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-27 18:59:47,140 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:47] "GET /api/students/students HTTP/1.1" 200 -
2025-05-27 18:59:51,630 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:51] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 10:21:01,630 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-28 10:21:01,630 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-28 10:21:01,630 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 10:21:01,631 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 10:21:01,631 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 10:21:01,631 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-28 10:21:01,631 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 10:21:16,298 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:21:16] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 10:21:16,303 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:21:16] "GET /api/users/users HTTP/1.1" 200 -
2025-05-28 10:21:16,304 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:21:16] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 10:21:16,305 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:21:16] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 10:21:16,309 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:21:16] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 10:21:16,315 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:21:16] "GET /api/users/users HTTP/1.1" 200 -
2025-05-28 10:21:16,630 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:21:16] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 10:21:30,450 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:21:30] "GET /api/users/users HTTP/1.1" 200 -
2025-05-28 10:21:32,998 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:21:32] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 10:21:33,300 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:21:33] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 10:21:34,654 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:21:34] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 10:21:37,347 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:21:37] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 10:21:37,349 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:21:37] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 10:21:39,549 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:21:39] "GET /api/users/users HTTP/1.1" 200 -
2025-05-28 10:22:16,701 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:22:16] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-28 10:22:16,712 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:22:16] "GET /api/users/users HTTP/1.1" 200 -
2025-05-28 10:22:24,228 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:22:24] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 10:22:24,230 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:22:24] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 10:22:41,372 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:22:41] "[35m[1mPOST /api/courses/courses HTTP/1.1[0m" 201 -
2025-05-28 10:22:41,694 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:22:41] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 10:22:45,157 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:22:45] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 10:23:19,538 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:23:19] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-28 10:23:19,562 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:23:19] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 201 -
2025-05-28 10:23:19,884 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:23:19] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 10:23:35,884 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:23:35] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-28 10:23:35,927 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:23:35] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 10:23:36,229 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:23:36] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 10:23:36,229 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:23:36] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 10:23:36,238 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:23:36] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 10:23:36,543 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:23:36] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 10:23:36,546 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:23:36] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 10:23:40,706 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:23:40] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 10:35:18,250 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:35:18] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 10:35:18,291 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:35:18] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 10:35:40,659 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:35:40] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 10:35:40,660 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:35:40] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 10:35:40,965 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:35:40] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 10:35:50,233 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:35:50] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-28 10:35:50,272 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:35:50] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 10:35:50,273 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:35:50] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 10:35:50,275 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:35:50] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 10:35:50,287 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:35:50] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 10:35:50,329 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:35:50] "GET /api/users/users HTTP/1.1" 200 -
2025-05-28 10:35:50,593 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:35:50] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 10:35:50,594 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:35:50] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 10:35:50,640 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:35:50] "GET /api/users/users HTTP/1.1" 200 -
2025-05-28 10:35:55,954 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:35:55] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 10:36:44,562 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:36:44] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-28 10:36:44,575 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:36:44] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 201 -
2025-05-28 10:36:44,898 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:36:44] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 10:36:50,848 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:36:50] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 10:36:50,849 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:36:50] "GET /api/users/users HTTP/1.1" 200 -
2025-05-28 10:36:50,849 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:36:50] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 10:36:50,850 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:36:50] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 10:37:01,114 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:37:01] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-28 10:37:01,440 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:37:01] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 10:37:01,449 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:37:01] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 10:37:01,449 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:37:01] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 10:37:01,450 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:37:01] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 10:37:01,694 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:37:01] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 10:37:01,763 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:37:01] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 10:38:55,799 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:38:55] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 10:52:14,943 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-28 10:52:14,974 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:52:14] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-28 10:52:17,223 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-28 10:52:17,224 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 10:52:17] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-28 10:59:26,672 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-28 10:59:26,672 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 10:59:26,708 - werkzeug - INFO -  * Restarting with stat
2025-05-28 10:59:28,704 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 10:59:28,744 - werkzeug - INFO -  * Debugger PIN: 330-************-05-28 10:59:50,498 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-28 10:59:50,498 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 10:59:50,501 - werkzeug - INFO -  * Restarting with stat
2025-05-28 10:59:52,705 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 10:59:52,711 - werkzeug - INFO -  * Debugger PIN: 330-************-05-28 11:00:41,712 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-28 11:00:41,720 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:00:41] "[31m[1mGET /api/users/users HTTP/1.1[0m" 401 -
2025-05-28 11:01:07,763 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:01:07] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-28 11:01:09,822 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:01:09] "GET /api/users/health HTTP/1.1" 200 -
2025-05-28 11:01:12,441 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-28 11:01:12,443 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:01:12] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-28 11:02:29,121 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:02:29] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-28 11:02:58,282 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-28 11:02:58,282 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 11:02:58,285 - werkzeug - INFO -  * Restarting with stat
2025-05-28 11:03:00,884 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 11:03:00,893 - werkzeug - INFO -  * Debugger PIN: 330-************-05-28 11:03:37,910 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:03:37] "GET /api/users/users/62 HTTP/1.1" 200 -
2025-05-28 11:03:37,918 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:03:37] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-28 11:04:14,841 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-28 11:04:14,841 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 11:04:14,843 - werkzeug - INFO -  * Restarting with stat
2025-05-28 11:04:16,942 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 11:04:16,954 - werkzeug - INFO -  * Debugger PIN: 330-************-05-28 11:04:21,482 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-28 11:04:21,482 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 11:04:21,486 - werkzeug - INFO -  * Restarting with stat
2025-05-28 11:04:23,476 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 11:04:23,488 - werkzeug - INFO -  * Debugger PIN: 330-************-05-28 11:04:29,476 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-28 11:04:29,476 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 11:04:29,480 - werkzeug - INFO -  * Restarting with stat
2025-05-28 11:04:31,552 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 11:04:31,557 - werkzeug - INFO -  * Debugger PIN: 330-************-05-28 11:05:03,286 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:05:03] "GET /api/users/users/62 HTTP/1.1" 200 -
2025-05-28 11:05:03,291 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:05:03] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-28 11:05:43,487 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:05:43] "GET /api/users/users/62 HTTP/1.1" 200 -
2025-05-28 11:05:43,490 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:05:43] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-28 11:05:45,992 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:05:45] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-28 11:05:48,059 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:05:48] "[31m[1mPOST /api/students/register HTTP/1.1[0m" 405 -
2025-05-28 11:06:10,674 - werkzeug - INFO -  * Detected change in 'A:\\pandor2-fl\\test_student_registration.py', reloading
2025-05-28 11:06:10,675 - werkzeug - INFO -  * Detected change in 'A:\\pandor2-fl\\test_student_registration.py', reloading
2025-05-28 11:06:10,915 - werkzeug - INFO -  * Restarting with stat
2025-05-28 11:06:10,922 - werkzeug - INFO -  * Restarting with stat
2025-05-28 11:06:11,032 - werkzeug - INFO -  * Detected change in 'A:\\pandor2-fl\\test_student_registration.py', reloading
2025-05-28 11:06:11,043 - werkzeug - INFO -  * Detected change in 'A:\\pandor2-fl\\test_student_registration.py', reloading
2025-05-28 11:06:11,050 - werkzeug - INFO -  * Detected change in 'A:\\pandor2-fl\\test_student_registration.py', reloading
2025-05-28 11:06:11,291 - werkzeug - INFO -  * Restarting with stat
2025-05-28 11:06:11,297 - werkzeug - INFO -  * Restarting with stat
2025-05-28 11:06:11,314 - werkzeug - INFO -  * Restarting with stat
2025-05-28 11:06:12,635 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 11:06:12,642 - werkzeug - INFO -  * Debugger PIN: 330-************-05-28 11:06:12,644 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 11:06:12,650 - werkzeug - INFO -  * Debugger PIN: 330-************-05-28 11:06:12,916 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 11:06:12,922 - werkzeug - INFO -  * Debugger PIN: 330-************-05-28 11:06:12,932 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 11:06:12,937 - werkzeug - INFO -  * Debugger PIN: 330-************-05-28 11:06:12,951 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 11:06:12,956 - werkzeug - INFO -  * Debugger PIN: 330-************-05-28 11:06:34,098 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:06:34] "GET /api/users/users/62 HTTP/1.1" 200 -
2025-05-28 11:06:34,104 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:06:34] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-28 11:06:36,193 - user_service.common.utils - ERROR - Error: Username already exists, Status: 400
2025-05-28 11:06:36,196 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:06:36] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 400 -
2025-05-28 11:06:43,448 - werkzeug - INFO -  * Detected change in 'A:\\pandor2-fl\\test_student_registration.py', reloading
2025-05-28 11:06:43,448 - werkzeug - INFO -  * Detected change in 'A:\\pandor2-fl\\test_student_registration.py', reloading
2025-05-28 11:06:43,668 - werkzeug - INFO -  * Restarting with stat
2025-05-28 11:06:43,668 - werkzeug - INFO -  * Restarting with stat
2025-05-28 11:06:43,718 - werkzeug - INFO -  * Detected change in 'A:\\pandor2-fl\\test_student_registration.py', reloading
2025-05-28 11:06:43,722 - werkzeug - INFO -  * Detected change in 'A:\\pandor2-fl\\test_student_registration.py', reloading
2025-05-28 11:06:43,725 - werkzeug - INFO -  * Detected change in 'A:\\pandor2-fl\\test_student_registration.py', reloading
2025-05-28 11:06:43,979 - werkzeug - INFO -  * Restarting with stat
2025-05-28 11:06:43,992 - werkzeug - INFO -  * Restarting with stat
2025-05-28 11:06:43,992 - werkzeug - INFO -  * Restarting with stat
2025-05-28 11:06:45,340 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 11:06:45,346 - werkzeug - INFO -  * Debugger PIN: 330-************-05-28 11:06:45,357 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 11:06:45,364 - werkzeug - INFO -  * Debugger PIN: 330-************-05-28 11:06:45,566 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 11:06:45,572 - werkzeug - INFO -  * Debugger PIN: 330-************-05-28 11:06:45,582 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 11:06:45,587 - werkzeug - INFO -  * Debugger PIN: 330-************-05-28 11:06:45,592 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 11:06:45,597 - werkzeug - INFO -  * Debugger PIN: 330-************-05-28 11:08:05,309 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-28 11:08:05,309 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 11:08:05,668 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
2025-05-28 11:08:05,668 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 11:08:06,072 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-05-28 11:08:06,072 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 11:08:06,499 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://************:5003
2025-05-28 11:08:06,499 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 11:08:07,182 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://************:5004
2025-05-28 11:08:07,183 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 11:08:17,423 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:08:17] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 11:08:17,434 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:08:17] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 11:08:17,434 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:08:17] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 11:08:17,437 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:08:17] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 11:08:17,450 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:08:17] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 11:08:17,451 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:08:17] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 11:08:30,979 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:08:30] "GET /api/users/users/28 HTTP/1.1" 200 -
2025-05-28 11:08:30,987 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:08:30] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-28 11:08:31,182 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:08:31] "GET /api/users/users HTTP/1.1" 200 -
2025-05-28 11:08:31,548 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:08:31] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 11:08:31,560 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:08:31] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 11:08:31,589 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:08:31] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 11:08:31,661 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:08:31] "GET /api/users/users HTTP/1.1" 200 -
2025-05-28 11:08:31,932 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:08:31] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 11:08:31,934 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:08:31] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 11:08:31,951 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:08:31] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 11:09:08,484 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:09:08] "GET /api/users/users HTTP/1.1" 200 -
2025-05-28 11:09:47,203 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:09:47] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-28 11:09:47,250 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:09:47] "GET /api/users/users HTTP/1.1" 200 -
2025-05-28 11:10:09,196 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:10:09] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 11:12:50,005 - user_service.common.utils - ERROR - Error: Email already exists, Status: 400
2025-05-28 11:12:50,011 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:12:50] "[31m[1mPOST /api/users/register HTTP/1.1[0m" 400 -
2025-05-28 11:13:03,916 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:13:03] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-28 11:13:03,988 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:13:03] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 201 -
2025-05-28 11:13:04,334 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:13:04] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 11:13:23,054 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:13:23] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 11:13:23,382 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:13:23] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 11:13:37,215 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:13:37] "GET /api/users/users/36 HTTP/1.1" 200 -
2025-05-28 11:13:37,222 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:13:37] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-28 11:13:37,656 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:13:37] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 11:13:37,660 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:13:37] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 11:13:37,665 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:13:37] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 11:13:37,696 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:13:37] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 11:13:37,866 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:13:37] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 11:13:38,007 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:13:38] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 11:13:43,169 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:13:43] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 11:14:26,916 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:14:26] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-28 11:14:26,963 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:14:26] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 201 -
2025-05-28 11:14:27,305 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:14:27] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 11:24:31,223 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:24:31] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 11:24:31,232 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:24:31] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 11:24:43,677 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:24:43] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 11:24:43,685 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:24:43] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 11:24:46,653 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:24:46] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 11:24:46,660 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:24:46] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 11:24:49,643 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:24:49] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 11:24:49,650 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 11:24:49] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 13:03:00,793 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:03:00] "GET /api/users/users/36 HTTP/1.1" 200 -
2025-05-28 13:03:00,797 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:03:00] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-28 13:03:00,881 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:03:00] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 13:03:01,151 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:03:01] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 13:03:01,180 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:03:01] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 13:03:01,183 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:03:01] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 13:03:01,192 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:03:01] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 13:03:01,505 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:03:01] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 13:04:29,681 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:04:29] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 13:04:29,681 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:04:29] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 13:04:29,681 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:04:29] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 13:04:29,689 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:04:29] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 13:04:30,009 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:04:30] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 13:04:40,662 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:04:40] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 13:04:40,662 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:04:40] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 13:04:40,662 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:04:40] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 13:04:40,669 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:04:40] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 13:04:40,982 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:04:40] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 13:04:40,984 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:04:40] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 13:04:52,666 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:04:52] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 13:04:52,667 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:04:52] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 13:04:52,668 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:04:52] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 13:04:52,674 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:04:52] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 13:04:52,979 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:04:52] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 13:04:52,980 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:04:52] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 13:05:03,668 - parent_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/parents/parents
2025-05-28 13:05:03,669 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:05:03] "[31m[1mGET /api/parents/parents HTTP/1.1[0m" 401 -
2025-05-28 13:05:03,667 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses
2025-05-28 13:05:03,667 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/students
2025-05-28 13:05:03,672 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:05:03] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 401 -
2025-05-28 13:05:03,672 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:05:03] "[31m[1mGET /api/students/students HTTP/1.1[0m" 401 -
2025-05-28 13:05:03,676 - parent_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/parents/parents
2025-05-28 13:05:03,677 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:05:03] "[31m[1mGET /api/parents/parents HTTP/1.1[0m" 401 -
2025-05-28 13:05:03,989 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses
2025-05-28 13:05:03,990 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:05:03] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 401 -
2025-05-28 13:05:03,989 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/students
2025-05-28 13:05:03,990 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:05:03] "[31m[1mGET /api/students/students HTTP/1.1[0m" 401 -
2025-05-28 13:05:14,654 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/students
2025-05-28 13:05:14,655 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:05:14] "[31m[1mGET /api/students/students HTTP/1.1[0m" 401 -
2025-05-28 13:05:14,655 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses
2025-05-28 13:05:14,655 - parent_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/parents/parents
2025-05-28 13:05:14,655 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:05:14] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 401 -
2025-05-28 13:05:14,656 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:05:14] "[31m[1mGET /api/parents/parents HTTP/1.1[0m" 401 -
2025-05-28 13:05:14,663 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/students
2025-05-28 13:05:14,663 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:05:14] "[31m[1mGET /api/students/students HTTP/1.1[0m" 401 -
2025-05-28 13:05:14,978 - parent_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/parents/parents
2025-05-28 13:05:14,979 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses
2025-05-28 13:05:14,979 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:05:14] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 401 -
2025-05-28 13:05:45,662 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses
2025-05-28 13:05:45,662 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/students
2025-05-28 13:05:45,662 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:05:45] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 401 -
2025-05-28 13:05:45,662 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:05:45] "[31m[1mGET /api/students/students HTTP/1.1[0m" 401 -
2025-05-28 13:05:45,662 - parent_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/parents/parents
2025-05-28 13:05:45,663 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:05:45] "[31m[1mGET /api/parents/parents HTTP/1.1[0m" 401 -
2025-05-28 13:05:45,667 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses
2025-05-28 13:05:45,667 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:05:45] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 401 -
2025-05-28 13:05:45,670 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/students
2025-05-28 13:05:45,670 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:05:45] "[31m[1mGET /api/students/students HTTP/1.1[0m" 401 -
2025-05-28 13:05:45,673 - parent_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/parents/parents
2025-05-28 13:05:45,674 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:05:45] "[31m[1mGET /api/parents/parents HTTP/1.1[0m" 401 -
2025-05-28 13:05:52,655 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/students
2025-05-28 13:05:52,655 - parent_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/parents/parents
2025-05-28 13:05:52,655 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:05:52] "[31m[1mGET /api/students/students HTTP/1.1[0m" 401 -
2025-05-28 13:05:52,655 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses
2025-05-28 13:05:52,655 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:05:52] "[31m[1mGET /api/parents/parents HTTP/1.1[0m" 401 -
2025-05-28 13:05:52,655 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:05:52] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 401 -
2025-05-28 13:05:52,662 - parent_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/parents/parents
2025-05-28 13:05:52,662 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/students
2025-05-28 13:05:52,663 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:05:52] "[31m[1mGET /api/students/students HTTP/1.1[0m" 401 -
2025-05-28 13:05:52,663 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses
2025-05-28 13:05:52,663 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:05:52] "[31m[1mGET /api/courses/courses HTTP/1.1[0m" 401 -
2025-05-28 13:09:18,225 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:09:18] "GET /api/users/users/28 HTTP/1.1" 200 -
2025-05-28 13:09:18,227 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:09:18] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-28 13:09:18,277 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:09:18] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 13:09:18,277 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:09:18] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 13:09:18,278 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:09:18] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 13:09:18,280 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:09:18] "GET /api/users/users HTTP/1.1" 200 -
2025-05-28 13:09:18,285 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:09:18] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 13:09:18,595 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:09:18] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 13:09:18,596 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:09:18] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 13:09:18,596 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:09:18] "GET /api/users/users HTTP/1.1" 200 -
2025-05-28 13:21:00,216 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:21:00] "GET /api/users/users/28 HTTP/1.1" 200 -
2025-05-28 13:21:00,218 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:21:00] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-28 13:21:00,252 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:21:00] "GET /api/users/users HTTP/1.1" 200 -
2025-05-28 13:21:00,558 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:21:00] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 13:21:00,559 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:21:00] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 13:21:00,561 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:21:00] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 13:21:00,566 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:21:00] "GET /api/users/users HTTP/1.1" 200 -
2025-05-28 13:21:00,883 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:21:00] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 13:21:00,883 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:21:00] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-28 13:21:00,883 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:21:00] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 13:21:10,566 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:21:10] "GET /api/users/users HTTP/1.1" 200 -
2025-05-28 13:21:14,572 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:21:14] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-28 13:21:14,573 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:21:14] "GET /api/students/students HTTP/1.1" 200 -
2025-05-28 13:21:17,484 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 13:21:17] "GET /api/students/students HTTP/1.1" 200 -
2025-05-29 10:39:58,615 - common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-29 10:39:58,616 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:39:58] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-29 10:41:04,077 - common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-29 10:41:04,078 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:41:04] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-29 10:41:50,444 - common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-29 10:41:50,444 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:41:50] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-29 10:44:30,951 - common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-29 10:44:30,952 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:44:30] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-29 10:45:24,066 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-05-29 10:45:24,066 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 10:45:24,066 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 10:45:24,228 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://***********:5002
2025-05-29 10:45:24,228 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 10:45:24,450 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://***********:5003
2025-05-29 10:45:24,450 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 10:45:24,609 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://***********:5004
2025-05-29 10:45:24,609 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 10:45:37,553 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:45:37] "GET /api/users/users/36 HTTP/1.1" 200 -
2025-05-29 10:45:37,555 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:45:37] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-29 10:45:37,628 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:45:37] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-29 10:45:37,634 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:45:37] "GET /api/students/students HTTP/1.1" 200 -
2025-05-29 10:45:37,636 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:45:37] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-29 10:45:37,637 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:45:37] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-29 10:45:37,644 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:45:37] "GET /api/students/students HTTP/1.1" 200 -
2025-05-29 10:45:37,945 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:45:37] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-29 10:46:49,462 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:46:49] "GET /api/students/students HTTP/1.1" 200 -
2025-05-29 10:48:22,599 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:48:22] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-29 10:48:22,621 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:48:22] "GET /api/students/students HTTP/1.1" 200 -
2025-05-29 10:50:38,438 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:50:38] "[35m[1mPOST /api/courses/map-student HTTP/1.1[0m" 201 -
2025-05-29 10:51:03,387 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:51:03] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-29 10:51:03,486 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:51:03] "GET /api/students/students HTTP/1.1" 200 -
2025-05-29 10:51:03,516 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:51:03] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-29 10:51:03,563 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:51:03] "GET /api/students/students HTTP/1.1" 200 -
2025-05-29 10:53:22,068 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:53:22] "[35m[1mPOST /api/courses/map-student HTTP/1.1[0m" 201 -
2025-05-29 10:55:28,832 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:55:28] "GET /api/students/students HTTP/1.1" 200 -
2025-05-29 10:56:29,006 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:56:29] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-29 10:56:29,325 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:56:29] "GET /api/students/students HTTP/1.1" 200 -
2025-05-29 10:56:56,355 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:56:56] "[31m[1mGET /api/students/student-parents/5 HTTP/1.1[0m" 405 -
2025-05-29 10:57:20,140 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:57:20] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-29 10:57:20,229 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:57:20] "GET /api/students/students HTTP/1.1" 200 -
2025-05-29 10:57:20,263 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:57:20] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-29 10:57:20,308 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:57:20] "GET /api/students/students HTTP/1.1" 200 -
2025-05-29 10:57:24,834 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:57:24] "[31m[1mGET /api/students/student-parents/12 HTTP/1.1[0m" 405 -
2025-05-29 10:58:40,666 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:58:40] "GET /api/users/users/28 HTTP/1.1" 200 -
2025-05-29 10:58:40,669 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:58:40] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-29 10:58:40,716 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:58:40] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-29 10:58:40,717 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:58:40] "GET /api/users/users HTTP/1.1" 200 -
2025-05-29 10:58:41,017 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:58:41] "GET /api/parents/parents HTTP/1.1" 200 -

2025-05-29 10:58:41,025 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:58:41] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-29 10:58:41,031 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:58:41] "GET /api/users/users HTTP/1.1" 200 -
2025-05-29 10:58:41,330 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:58:41] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-29 10:58:41,332 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:58:41] "GET /api/students/students HTTP/1.1" 200 -
2025-05-29 10:58:45,508 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:58:45] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-29 10:58:45,783 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:58:45] "GET /api/students/students HTTP/1.1" 200 -
2025-05-29 10:58:50,970 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:58:50] "[31m[1mGET /api/students/student-parents/5 HTTP/1.1[0m" 405 -
2025-05-29 10:59:58,945 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 10:59:58] "[31m[1mGET /api/students/student-parents/19 HTTP/1.1[0m" 405 -
2025-05-29 11:02:15,266 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:02:15] "GET /api/users/users/36 HTTP/1.1" 200 -
2025-05-29 11:02:15,267 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:02:15] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-29 11:06:21,777 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:06:21] "[31m[1mGET /api/students/student-parents/20 HTTP/1.1[0m" 405 -
2025-05-29 11:06:24,117 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:06:24] "[31m[1mGET /api/students/student-parents/22 HTTP/1.1[0m" 405 -
2025-05-29 11:06:34,742 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:06:34] "[31m[1mGET /api/parents/9 HTTP/1.1[0m" 405 -
2025-05-29 11:06:34,743 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-29 11:06:34,747 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:06:34] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-29 11:07:00,201 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:07:00] "[31m[1mGET /api/parents/5 HTTP/1.1[0m" 405 -
2025-05-29 11:07:00,202 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-29 11:07:00,204 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:07:00] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-29 11:08:59,101 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:08:59] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-29 11:08:59,102 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:08:59] "GET /api/students/students HTTP/1.1" 200 -
2025-05-29 11:08:59,108 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:08:59] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-29 11:08:59,109 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:08:59] "GET /api/students/students HTTP/1.1" 200 -
2025-05-29 11:10:14,526 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-05-29 11:10:14,527 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 11:10:14,615 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://***********:5001
2025-05-29 11:10:14,615 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 11:10:14,815 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://***********:5002
2025-05-29 11:10:14,815 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 11:10:14,988 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://***********:5003
2025-05-29 11:10:14,988 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 11:10:15,298 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://***********:5004
2025-05-29 11:10:15,298 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 11:10:23,020 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:10:23] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-29 11:10:23,026 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:10:23] "GET /api/students/students HTTP/1.1" 200 -
2025-05-29 11:10:23,027 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:10:23] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-29 11:10:23,028 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:10:23] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-29 11:10:23,036 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:10:23] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-29 11:10:23,036 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:10:23] "GET /api/students/students HTTP/1.1" 200 -
2025-05-29 11:10:29,159 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:10:29] "GET /api/users/users/28 HTTP/1.1" 200 -
2025-05-29 11:10:29,162 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:10:29] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-29 11:10:29,201 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:10:29] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-29 11:10:29,209 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:10:29] "GET /api/users/users HTTP/1.1" 200 -
2025-05-29 11:10:29,511 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:10:29] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-29 11:10:29,512 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:10:29] "GET /api/students/students HTTP/1.1" 200 -
2025-05-29 11:10:29,513 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:10:29] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-29 11:10:29,519 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:10:29] "GET /api/users/users HTTP/1.1" 200 -
2025-05-29 11:10:29,822 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:10:29] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-29 11:10:29,830 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:10:29] "GET /api/students/students HTTP/1.1" 200 -
2025-05-29 11:10:34,410 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:10:34] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-29 11:10:34,655 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:10:34] "GET /api/students/students HTTP/1.1" 200 -
2025-05-29 11:10:45,999 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:10:45] "[31m[1mGET /api/students/student-parents/16 HTTP/1.1[0m" 405 -
2025-05-29 11:10:56,586 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:10:56] "[31m[1mGET /api/parents/6 HTTP/1.1[0m" 405 -
2025-05-29 11:10:56,586 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-29 11:10:56,587 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:10:56] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-29 11:11:17,607 - student_service.common.middleware - WARNING - Expired token for path: /api/students/map-parent
2025-05-29 11:11:17,608 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:11:17] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 401 -
2025-05-29 11:11:41,453 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:11:41] "GET /api/users/users/28 HTTP/1.1" 200 -
2025-05-29 11:11:41,455 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:11:41] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-29 11:12:24,660 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:12:24] "[31m[1mPOST /api/courses/map-student HTTP/1.1[0m" 400 -
2025-05-29 11:12:38,166 - student_service.common.utils - ERROR - Error: Student not found, Status: 404
2025-05-29 11:12:38,167 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:12:38] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-29 11:19:03,850 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-05-29 11:19:03,850 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 11:19:04,020 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://***********:5001
2025-05-29 11:19:04,020 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 11:19:04,221 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://***********:5002
2025-05-29 11:19:04,221 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 11:19:04,405 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://***********:5003
2025-05-29 11:19:04,406 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 11:19:04,638 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://***********:5004
2025-05-29 11:19:04,638 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 11:19:33,935 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:19:33] "GET /api/users/users/28 HTTP/1.1" 200 -
2025-05-29 11:19:33,936 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:19:33] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-29 11:20:30,167 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:20:30] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-29 11:20:34,431 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:20:34] "GET /api/users/users/28 HTTP/1.1" 200 -
2025-05-29 11:20:34,433 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:20:34] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-29 11:20:36,478 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:20:36] "GET /api/auth/verify HTTP/1.1" 200 -
2025-05-29 11:20:38,513 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:20:38] "GET /api/users/health HTTP/1.1" 200 -
2025-05-29 11:20:40,539 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:20:40] "GET /api/users/users HTTP/1.1" 200 -
2025-05-29 11:20:42,780 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:20:42] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-29 11:20:44,818 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:20:44] "GET /api/users/users/67 HTTP/1.1" 200 -
2025-05-29 11:20:46,839 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:20:46] "GET /api/students/health HTTP/1.1" 200 -
2025-05-29 11:20:48,873 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:20:48] "GET /api/students/students HTTP/1.1" 200 -
2025-05-29 11:20:50,916 - student_service.common.utils - ERROR - Error: Missing required fields: user_id, first_name, last_name, Status: 400
2025-05-29 11:20:50,916 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:20:50] "[31m[1mPOST /api/students/students HTTP/1.1[0m" 400 -
2025-05-29 11:20:52,949 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:20:52] "GET /api/parents/health HTTP/1.1" 200 -
2025-05-29 11:20:54,973 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:20:54] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-29 11:20:57,005 - parent_service.common.utils - ERROR - Error: Missing required fields: user_id, first_name, last_name, Status: 400
2025-05-29 11:20:57,006 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:20:57] "[31m[1mPOST /api/parents/parents HTTP/1.1[0m" 400 -
2025-05-29 11:20:59,046 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:20:59] "GET /api/courses/health HTTP/1.1" 200 -
2025-05-29 11:21:01,074 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:21:01] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-29 11:21:03,119 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:21:03] "[35m[1mPOST /api/courses/courses HTTP/1.1[0m" 201 -
2025-05-29 11:21:05,150 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:21:05] "GET /api/courses/courses/11 HTTP/1.1" 200 -
2025-05-29 11:21:07,193 - student_service.common.utils - ERROR - Error: Student not found, Status: 404
2025-05-29 11:21:07,194 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:21:07] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-29 11:21:09,238 - course_service.common.utils - ERROR - Error: Course not found, Status: 404
2025-05-29 11:21:09,240 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:21:09] "[33mPOST /api/courses/map-student HTTP/1.1[0m" 404 -
2025-05-29 11:23:06,569 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:06] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-29 11:23:10,817 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:10] "GET /api/users/users/28 HTTP/1.1" 200 -
2025-05-29 11:23:10,818 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:10] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-29 11:23:12,833 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:12] "GET /api/auth/verify HTTP/1.1" 200 -
2025-05-29 11:23:14,854 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:14] "GET /api/users/health HTTP/1.1" 200 -
2025-05-29 11:23:16,890 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:16] "GET /api/users/users HTTP/1.1" 200 -
2025-05-29 11:23:19,116 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:19] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-29 11:23:21,137 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:21] "GET /api/users/users/68 HTTP/1.1" 200 -
2025-05-29 11:23:23,163 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:23] "GET /api/students/health HTTP/1.1" 200 -
2025-05-29 11:23:25,194 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:25] "GET /api/students/students HTTP/1.1" 200 -
2025-05-29 11:23:27,432 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:27] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-29 11:23:29,476 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:29] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 201 -
2025-05-29 11:23:31,492 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:31] "GET /api/students/students/23 HTTP/1.1" 200 -
2025-05-29 11:23:33,518 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:33] "GET /api/parents/health HTTP/1.1" 200 -
2025-05-29 11:23:35,538 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:35] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-29 11:23:37,763 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:37] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-29 11:23:39,813 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:39] "[35m[1mPOST /api/parents/parents HTTP/1.1[0m" 201 -
2025-05-29 11:23:41,833 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:41] "GET /api/parents/parents/10 HTTP/1.1" 200 -
2025-05-29 11:23:43,862 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:43] "GET /api/courses/health HTTP/1.1" 200 -
2025-05-29 11:23:45,884 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:45] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-29 11:23:47,909 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:47] "[35m[1mPOST /api/courses/courses HTTP/1.1[0m" 201 -
2025-05-29 11:23:49,933 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:49] "GET /api/courses/courses/12 HTTP/1.1" 200 -
2025-05-29 11:23:53,996 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:53] "[31m[1mGET /api/parents/10 HTTP/1.1[0m" 405 -
2025-05-29 11:23:53,996 - student_service.common.utils - ERROR - Error: Parent not found or service unavailable, Status: 404
2025-05-29 11:23:53,998 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:53] "[33mPOST /api/students/map-parent HTTP/1.1[0m" 404 -
2025-05-29 11:23:56,028 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:56] "GET /api/students/parent-students/10 HTTP/1.1" 200 -
2025-05-29 11:23:58,063 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:23:58] "[35m[1mPOST /api/courses/map-student HTTP/1.1[0m" 201 -
2025-05-29 11:24:00,092 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:24:00] "GET /api/courses/student-courses/23 HTTP/1.1" 200 -
2025-05-29 11:24:57,634 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:24:57] "[31m[1mGET /api/parents/10 HTTP/1.1[0m" 405 -
2025-05-29 11:25:03,789 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:25:03] "GET /api/parents/parents/10 HTTP/1.1" 200 -
2025-05-29 11:26:07,636 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-05-29 11:26:07,637 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 11:26:07,804 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://***********:5001
2025-05-29 11:26:07,804 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 11:26:08,025 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://***********:5002
2025-05-29 11:26:08,025 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 11:26:08,230 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://***********:5003
2025-05-29 11:26:08,230 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 11:26:08,428 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://***********:5004
2025-05-29 11:26:08,428 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 11:26:50,845 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:26:50] "GET /api/users/users/28 HTTP/1.1" 200 -
2025-05-29 11:26:50,847 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:26:50] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-29 11:26:53,084 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:26:53] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-29 11:26:55,130 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:26:55] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 201 -
2025-05-29 11:26:57,395 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:26:57] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-29 11:26:59,442 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:26:59] "[35m[1mPOST /api/parents/parents HTTP/1.1[0m" 201 -
2025-05-29 11:27:03,537 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:03] "GET /api/parents/parents/11 HTTP/1.1" 200 -
2025-05-29 11:27:03,548 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:03] "[35m[1mPOST /api/students/map-parent HTTP/1.1[0m" 201 -
2025-05-29 11:27:11,688 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:11] "GET /api/auth/health HTTP/1.1" 200 -
2025-05-29 11:27:15,928 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:15] "GET /api/users/users/28 HTTP/1.1" 200 -
2025-05-29 11:27:15,929 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:15] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-29 11:27:17,965 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:17] "GET /api/auth/verify HTTP/1.1" 200 -
2025-05-29 11:27:19,985 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:19] "GET /api/users/health HTTP/1.1" 200 -
2025-05-29 11:27:22,004 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:22] "GET /api/users/users HTTP/1.1" 200 -
2025-05-29 11:27:24,251 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:24] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-29 11:27:26,272 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:26] "GET /api/users/users/73 HTTP/1.1" 200 -
2025-05-29 11:27:28,304 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:28] "GET /api/students/health HTTP/1.1" 200 -
2025-05-29 11:27:30,326 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:30] "GET /api/students/students HTTP/1.1" 200 -
2025-05-29 11:27:32,564 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:32] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-29 11:27:34,590 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:34] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 201 -
2025-05-29 11:27:36,613 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:36] "GET /api/students/students/25 HTTP/1.1" 200 -
2025-05-29 11:27:38,644 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:38] "GET /api/parents/health HTTP/1.1" 200 -
2025-05-29 11:27:40,661 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:40] "GET /api/parents/parents HTTP/1.1" 200 -
2025-05-29 11:27:42,890 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:42] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-29 11:27:44,914 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:44] "[35m[1mPOST /api/parents/parents HTTP/1.1[0m" 201 -
2025-05-29 11:27:46,942 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:46] "GET /api/parents/parents/12 HTTP/1.1" 200 -
2025-05-29 11:27:48,962 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:48] "GET /api/courses/health HTTP/1.1" 200 -
2025-05-29 11:27:51,000 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:51] "GET /api/courses/courses HTTP/1.1" 200 -
2025-05-29 11:27:53,026 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:53] "[35m[1mPOST /api/courses/courses HTTP/1.1[0m" 201 -
2025-05-29 11:27:55,061 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:55] "GET /api/courses/courses/13 HTTP/1.1" 200 -
2025-05-29 11:27:59,114 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:59] "GET /api/parents/parents/12 HTTP/1.1" 200 -
2025-05-29 11:27:59,120 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:27:59] "[35m[1mPOST /api/students/map-parent HTTP/1.1[0m" 201 -
2025-05-29 11:28:01,144 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:28:01] "GET /api/students/parent-students/12 HTTP/1.1" 200 -
2025-05-29 11:28:03,180 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:28:03] "[35m[1mPOST /api/courses/map-student HTTP/1.1[0m" 201 -
2025-05-29 11:28:05,205 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:28:05] "GET /api/courses/student-courses/25 HTTP/1.1" 200 -
2025-05-29 11:29:21,132 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:29:21] "GET /api/users/users/28 HTTP/1.1" 200 -
2025-05-29 11:29:21,133 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:29:21] "POST /api/auth/login HTTP/1.1" 200 -
2025-05-29 11:29:23,150 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-29 11:29:23,154 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:29:23] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-29 11:29:25,204 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-29 11:29:25,205 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:29:25] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-29 11:29:27,244 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-29 11:29:27,245 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:29:27] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-29 11:29:29,278 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-29 11:29:29,280 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:29:29] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-29 11:29:31,306 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-29 11:29:31,307 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:29:31] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-29 11:29:33,558 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:29:33] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-29 11:29:35,796 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:29:35] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-29 11:29:38,037 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:29:38] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-29 11:29:40,286 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:29:40] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-29 11:29:42,536 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:29:42] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-29 11:29:44,765 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:29:44] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-29 11:29:47,017 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:29:47] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-29 11:29:49,263 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:29:49] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-29 11:29:51,497 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:29:51] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-29 11:29:53,762 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:29:53] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-29 11:29:55,792 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-29 11:29:55,793 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:29:55] "[31m[1mGET /api/users/users HTTP/1.1[0m" 401 -
2025-05-29 11:29:57,812 - student_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/students/students
2025-05-29 11:29:57,813 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:29:57] "[31m[1mGET /api/students/students HTTP/1.1[0m" 401 -
2025-05-29 11:29:59,838 - parent_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/parents/parents
2025-05-29 11:29:59,839 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:29:59] "[31m[1mGET /api/parents/parents HTTP/1.1[0m" 401 -
2025-05-29 11:30:01,867 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses
2025-05-29 11:30:01,867 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:30:01] "[31m[1mPOST /api/courses/courses HTTP/1.1[0m" 401 -
2025-05-29 11:30:03,888 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/users, error: Not enough segments
2025-05-29 11:30:03,888 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:30:03] "[31m[1mGET /api/users/users HTTP/1.1[0m" 401 -
2025-05-29 11:30:06,127 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:30:06] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-29 11:30:08,174 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:30:08] "[35m[1mPOST /api/students/students HTTP/1.1[0m" 201 -
2025-05-29 11:30:10,408 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:30:10] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-29 11:30:12,436 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:30:12] "[35m[1mPOST /api/parents/parents HTTP/1.1[0m" 201 -
2025-05-29 11:30:14,692 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:30:14] "[35m[1mPOST /api/users/register HTTP/1.1[0m" 201 -
2025-05-29 11:30:16,715 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:30:16] "[35m[1mPOST /api/parents/parents HTTP/1.1[0m" 201 -
2025-05-29 11:30:20,755 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:30:20] "GET /api/parents/parents/13 HTTP/1.1" 200 -
2025-05-29 11:30:20,764 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:30:20] "[35m[1mPOST /api/students/map-parent HTTP/1.1[0m" 201 -
2025-05-29 11:30:24,820 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:30:24] "GET /api/parents/parents/14 HTTP/1.1" 200 -
2025-05-29 11:30:24,822 - student_service.common.utils - ERROR - Error: Student already has a parent mapped. Only one parent per student is allowed., Status: 400
2025-05-29 11:30:24,823 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:30:24] "[31m[1mPOST /api/students/map-parent HTTP/1.1[0m" 400 -
2025-05-29 11:30:26,863 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 11:30:26] "OPTIONS /api/auth/login HTTP/1.1" 200 -
